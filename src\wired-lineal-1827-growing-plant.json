{"v": "5.8.1", "fr": 60, "ip": 0, "op": 150, "w": 430, "h": 430, "nm": "wired-lineal-1827-growing-plant", "ddd": 0, "assets": [{"id": "comp_0", "nm": "hover-pinch", "fr": 60, "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "S<PERSON>pe Layer 5", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.242, "y": 0.591}, "o": {"x": 0.294, "y": 0.403}, "t": 34, "s": [219.812, 293, 0], "to": [5.119, -77.743, 0], "ti": [33.326, -5.248, 0]}, {"i": {"x": 0.978, "y": 0.975}, "o": {"x": 0.746, "y": 0.31}, "t": 57, "s": [159.726, 148.251, 0], "to": [-27.438, 4.32, 0], "ti": [9.558, -149.575, 0]}, {"t": 81, "s": [94.312, 359, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [10.812, 73, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 1]}, "o": {"x": [0.65, 0.65, 0.65], "y": [0, 0, 0]}, "t": 72.457, "s": [-100, 100, 100]}, {"t": 95, "s": [0, 0, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[10.5, 73], [11.125, 73]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.071, 0.075, 0.192, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-1827-growing-plant').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 3.5, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 1), comp('wired-lineal-1827-growing-plant').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 34, "op": 81, "st": 34, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "Shape Layer 4", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.648, "y": 0.371}, "o": {"x": 0.276, "y": 0.448}, "t": 34, "s": [219.812, 293, 0], "to": [2.333, -80.75, 0], "ti": [-3.833, -201.25, 0]}, {"t": 65, "s": [118.312, 332, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [10.812, 73, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 1]}, "o": {"x": [0.65, 0.65, 0.65], "y": [0, 0, 0]}, "t": 56, "s": [-100, 100, 100]}, {"t": 73, "s": [0, 0, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[10.5, 73], [11.125, 73]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.071, 0.075, 0.192, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-1827-growing-plant').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 3.5, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 1), comp('wired-lineal-1827-growing-plant').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 34, "op": 66, "st": 34, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "Shape Layer 3", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.242, "y": 0.591}, "o": {"x": 0.294, "y": 0.403}, "t": 34, "s": [219.812, 293, 0], "to": [-5.119, -77.743, 0], "ti": [-33.326, -5.248, 0]}, {"i": {"x": 0.978, "y": 0.975}, "o": {"x": 0.746, "y": 0.31}, "t": 57, "s": [279.899, 148.251, 0], "to": [27.438, 4.32, 0], "ti": [-9.558, -149.575, 0]}, {"t": 81, "s": [345.312, 359, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [10.812, 73, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 1]}, "o": {"x": [0.65, 0.65, 0.65], "y": [0, 0, 0]}, "t": 72.457, "s": [100, 100, 100]}, {"t": 95, "s": [0, 0, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[10.5, 73], [11.125, 73]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.071, 0.075, 0.192, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-1827-growing-plant').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 3.5, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 1), comp('wired-lineal-1827-growing-plant').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 34, "op": 81, "st": 34, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "Shape Layer 2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.648, "y": 0.371}, "o": {"x": 0.276, "y": 0.448}, "t": 34, "s": [219.812, 293, 0], "to": [-2.333, -80.75, 0], "ti": [3.833, -201.25, 0]}, {"t": 65, "s": [321.312, 332, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [10.812, 73, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 1]}, "o": {"x": [0.65, 0.65, 0.65], "y": [0, 0, 0]}, "t": 56, "s": [100, 100, 100]}, {"t": 73, "s": [0, 0, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[10.5, 73], [11.125, 73]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.071, 0.075, 0.192, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-1827-growing-plant').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 3.5, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 1), comp('wired-lineal-1827-growing-plant').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 34, "op": 66, "st": 34, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "outline 2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [215, 337.755, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -25.08], [0, 0], [-25.09, 0], [-6.76, -4], [-27.03, 0], [-12.71, -21.77], [-8.42, 0]], "o": [[0, 0], [0, -25.08], [8.42, 0], [12.71, -21.77], [27.03, 0], [6.76, -4], [25.09, 0]], "v": [[131.85, 37.755], [-131.85, 37.755], [-86.42, -7.665], [-63.35, -1.365], [0, -37.755], [63.35, -1.365], [86.42, -7.665]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.071, 0.075, 0.192, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-1827-growing-plant').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 3.5, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 1), comp('wired-lineal-1827-growing-plant').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 844, "st": 0, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "outline 2 fill", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [214.785, 337.06, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [242.611, 358.607, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-9.74, -4.79], [1.55, 0], [14.912, -23.69], [1.381, 0.72], [8.674, 0], [7.5, -19.64], [0, 0], [-0.124, 1.752], [-23.996, 0], [-6.189, -3.167], [-0.831, 1.308], [-26.031, 0]], "o": [[-1.53, -0.09], [-30.067, 0], [-0.83, 1.318], [-7.198, -3.751], [-22.28, 0], [0, 0], [-1.757, 0], [1.668, -23.572], [7.425, 0], [1.379, 0.706], [13.006, -20.456], [11.58, 0]], "v": [[82.012, -30.29], [77.382, -30.42], [6.026, 9.012], [2.109, 10.041], [-21.948, 4.17], [-70.748, 37.76], [-79.012, 37.76], [-82.004, 34.507], [-36.688, -7.67], [-16.062, -2.706], [-12.165, -3.745], [49.732, -37.76]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.698, 0.408, 0.212, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-1827-growing-plant').layer('control').effect('tertiary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".tertiary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "tertiary"}, {"ty": "tr", "p": {"a": 0, "k": [192.879, 358.607], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 50, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 1, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[1.757, 0], [0, 0], [-0.124, 1.752], [-23.996, 0], [-6.189, -3.166], [-0.831, 1.307], [-26.031, 0], [-13.006, -20.446], [-1.38, 0.706], [-7.425, 0], [-1.669, -23.562]], "o": [[0, 0], [-1.757, 0], [1.669, -23.562], [7.425, 0], [1.379, 0.705], [13.006, -20.447], [26.031, 0], [0.832, 1.307], [6.189, -3.166], [23.996, 0], [0.124, 1.752]], "v": [[128.744, 37.755], [-128.744, 37.755], [-131.735, 34.502], [-86.42, -7.665], [-65.792, -2.711], [-61.897, -3.749], [0, -37.755], [61.896, -3.75], [65.793, -2.711], [86.42, -7.665], [131.735, 34.502]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.698, 0.408, 0.212, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-1827-growing-plant').layer('control').effect('tertiary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".tertiary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "tertiary"}, {"ty": "tr", "p": {"a": 0, "k": [242.611, 358.607], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 844, "st": 0, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "outline 3", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [214.215, 300, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [249.215, 335, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-8.836, -48.127], [0.33, -7.91], [-9.97, 23.673], [-16.052, 19.969], [-19.584, 21.723]], "o": [[16.896, 9.023], [1.673, 9.113], [-0.984, -10.773], [4.167, -9.894], [11.157, -13.879], [0, 0]], "v": [[-71.429, -39.055], [-10.511, 48.015], [-8.742, 73.66], [-0.206, 18.027], [25.169, -19.368], [71.429, -73.66]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.35], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 89, "s": [41]}, {"t": 150, "s": [0]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.35], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 89, "s": [41]}, {"t": 150, "s": [100]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.071, 0.075, 0.192, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-1827-growing-plant').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 3.5, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 1), comp('wired-lineal-1827-growing-plant').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [259.915, 261.34], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 89, "op": 933, "st": 89, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 4, "nm": "outline 5", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.35], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 89, "s": [50]}, {"t": 150, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.35, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 89, "s": [216.577, 298.823, 0], "to": [-0.583, -24.5, 0], "ti": [19.083, 22, 0]}, {"t": 150, "s": [192.077, 223.823, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [227.077, 258.823, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.35, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 89, "s": [{"i": [[-0.162, -0.143], [0.024, -0.151], [0.162, 0.143], [-0.024, 0.151]], "o": [[0.162, 0.143], [-0.147, 0.042], [-0.162, -0.143], [0.147, -0.042]], "v": [[47.119, 40.743], [47.336, 41.228], [46.828, 41.071], [46.611, 40.586]], "c": true}]}, {"t": 150, "s": [{"i": [[-20.119, -17.793], [2.973, -18.787], [20.119, 17.793], [-2.973, 18.787]], "o": [[20.119, 17.793], [-18.283, 5.248], [-20.119, -17.793], [18.283, -5.248]], "v": [[18.042, -20.401], [45.078, 39.866], [-18.041, 20.401], [-45.078, -39.866]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.071, 0.075, 0.192, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-1827-growing-plant').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 3.5, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 1), comp('wired-lineal-1827-growing-plant').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [181.48, 217.246], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 89, "op": 933, "st": 89, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 4, "nm": "Leaf L 2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": -50, "ix": 10}, "p": {"a": 0, "k": [56.768, 202.723, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [174.09, 203.102, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.898}, "o": {"x": 0.425, "y": 0}, "t": 89, "s": [{"i": [[0.012, -0.012], [0, 0], [0.01, 0.003], [0, 0], [0.001, 0], [0.001, 0.001], [0.007, 0.004], [0.001, 0.001], [0.005, 0.003], [0.003, 0.002], [0.005, 0.003], [0.001, 0], [0.04, 0.035], [0.001, 0.001], [0.054, 0.082], [0.003, 0.005], [0.007, 0.012], [0.003, 0.005], [0.01, 0.021], [0.009, 0.021], [0.014, 0.038], [0.015, 0.084], [0.004, 0.058], [0, 0.023], [-0.001, 0.022], [-0.001, 0.011], [-0.002, 0.022], [-0.003, 0.021], [-0.11, 0.128], [-0.005, 0.005], [-0.001, 0.001], [-0.004, 0.004], [-0.002, 0.002], [-0.001, 0.001], [0, 0], [-0.011, -0.005], [-0.007, -0.003], [0, 0], [-0.005, -0.003], [-0.006, -0.003], [-0.005, -0.003], [-0.003, -0.002], [-0.002, -0.001], [-0.006, -0.004], [-0.009, -0.006], [-0.004, -0.003], [-0.02, -0.018], [-0.001, -0.001], [-0.007, -0.006], [-0.007, -0.007], [0.011, -0.012], [-0.072, -0.394], [-0.146, -0.165]], "o": [[0, 0], [-0.008, 0.008], [0, 0], [-0.001, 0], [-0.001, -0.001], [-0.007, -0.003], [-0.001, 0], [-0.006, -0.003], [-0.003, -0.002], [-0.005, -0.003], [0, 0], [-0.043, -0.026], [-0.001, 0], [-0.066, -0.059], [-0.003, -0.005], [-0.007, -0.011], [-0.003, -0.004], [-0.011, -0.02], [-0.01, -0.02], [-0.016, -0.036], [-0.027, -0.076], [-0.011, -0.06], [-0.002, -0.023], [0, -0.023], [0, -0.011], [0.001, -0.022], [0.002, -0.022], [0.031, -0.204], [0.005, -0.006], [0.001, -0.001], [0.004, -0.005], [0.002, -0.002], [0.001, -0.001], [0, 0], [0.009, -0.008], [0.007, 0.003], [0, 0], [0.005, 0.003], [0.006, 0.003], [0.004, 0.003], [0.004, 0.002], [0.002, 0.001], [0.006, 0.003], [0.009, 0.006], [0.004, 0.003], [0.021, 0.016], [0.001, 0.001], [0.007, 0.006], [0.007, 0.007], [0.011, 0.011], [-0.19, 0.221], [0.048, 0.265], [0.011, 0.013]], "v": [[29.736, 186.408], [29.736, 186.409], [29.706, 186.417], [29.706, 186.416], [29.702, 186.415], [29.698, 186.413], [29.675, 186.402], [29.672, 186.401], [29.656, 186.392], [29.646, 186.387], [29.63, 186.378], [29.629, 186.377], [29.503, 186.285], [29.501, 186.283], [29.32, 186.072], [29.311, 186.058], [29.289, 186.023], [29.281, 186.01], [29.248, 185.95], [29.219, 185.889], [29.174, 185.778], [29.11, 185.537], [29.088, 185.361], [29.085, 185.292], [29.086, 185.224], [29.087, 185.191], [29.092, 185.125], [29.1, 185.06], [29.318, 184.55], [29.332, 184.534], [29.335, 184.531], [29.348, 184.517], [29.355, 184.51], [29.357, 184.508], [29.358, 184.507], [29.391, 184.502], [29.411, 184.511], [29.412, 184.512], [29.428, 184.52], [29.446, 184.529], [29.459, 184.537], [29.47, 184.543], [29.476, 184.547], [29.493, 184.558], [29.52, 184.576], [29.533, 184.586], [29.595, 184.637], [29.597, 184.639], [29.617, 184.657], [29.638, 184.678], [29.64, 184.72], [29.433, 185.707], [29.738, 186.365]], "c": true}]}, {"i": {"x": 0.88, "y": 1}, "o": {"x": 0.43, "y": 0.263}, "t": 93, "s": [{"i": [[0.102, 0.03], [0, 0], [0.017, 0.069], [0.001, 0.002], [0.001, 0.008], [-0.001, 0.011], [0.002, 0.054], [-0.001, 0.007], [0, 0.04], [-0.001, 0.025], [0, 0.041], [0, 0.004], [-0.074, 0.343], [-0.001, 0.005], [-0.289, 0.573], [-0.018, 0.031], [-0.045, 0.079], [-0.017, 0.03], [-0.082, 0.128], [-0.086, 0.12], [-0.171, 0.201], [-0.425, 0.36], [-0.312, 0.21], [-0.126, 0.076], [-0.127, 0.069], [-0.064, 0.033], [-0.129, 0.058], [-0.129, 0.051], [-1.062, -0.214], [-0.045, -0.01], [-0.008, -0.002], [-0.039, -0.01], [-0.02, -0.006], [-0.006, -0.002], [-0.002, -0.001], [-0.007, -0.079], [-0.002, -0.049], [-0.001, -0.003], [-0.001, -0.04], [-0.001, -0.043], [0.001, -0.034], [0.001, -0.027], [0.001, -0.015], [0.003, -0.044], [0.008, -0.071], [0.005, -0.036], [0.039, -0.176], [0.002, -0.005], [0.015, -0.059], [0.018, -0.062], [0.101, 0.02], [1.989, -1.689], [0.477, -1.363]], "o": [[0, 0], [-0.067, -0.02], [-0.001, -0.002], [-0.002, -0.008], [-0.001, -0.01], [-0.004, -0.054], [-0.001, -0.007], [-0.002, -0.04], [-0.001, -0.024], [0, -0.041], [-0.001, -0.003], [0.009, -0.33], [0, -0.007], [0.122, -0.569], [0.016, -0.032], [0.04, -0.079], [0.016, -0.031], [0.074, -0.128], [0.079, -0.123], [0.149, -0.209], [0.343, -0.403], [0.3, -0.255], [0.125, -0.085], [0.127, -0.077], [0.065, -0.034], [0.129, -0.066], [0.129, -0.058], [1.241, -0.489], [0.046, 0.008], [0.009, 0.003], [0.039, 0.009], [0.02, 0.005], [0.006, 0.001], [0.002, 0], [0.073, 0.024], [0.004, 0.048], [0.001, 0.002], [0.002, 0.039], [0.001, 0.042], [0, 0.034], [0, 0.027], [0, 0.016], [-0.002, 0.043], [-0.006, 0.07], [-0.003, 0.035], [-0.022, 0.173], [-0.001, 0.005], [-0.012, 0.059], [-0.016, 0.062], [-0.029, 0.102], [-1.834, -0.366], [-1.335, 1.134], [-0.036, 0.104]], "v": [[34.583, 180.801], [34.579, 180.8], [34.444, 180.657], [34.443, 180.65], [34.44, 180.627], [34.438, 180.596], [34.428, 180.433], [34.427, 180.411], [34.424, 180.291], [34.423, 180.217], [34.424, 180.095], [34.425, 180.083], [34.549, 179.071], [34.553, 179.053], [35.173, 177.33], [35.222, 177.236], [35.35, 176.999], [35.401, 176.908], [35.637, 176.526], [35.885, 176.161], [36.366, 175.546], [37.518, 174.397], [38.439, 173.702], [38.817, 173.461], [39.2, 173.243], [39.392, 173.142], [39.778, 172.956], [40.165, 172.793], [43.706, 172.373], [43.842, 172.401], [43.867, 172.408], [43.983, 172.437], [44.044, 172.454], [44.063, 172.459], [44.07, 172.463], [44.203, 172.633], [44.213, 172.777], [44.214, 172.786], [44.219, 172.906], [44.221, 173.035], [44.219, 173.138], [44.217, 173.22], [44.215, 173.267], [44.208, 173.398], [44.188, 173.609], [44.176, 173.716], [44.083, 174.239], [44.08, 174.254], [44.038, 174.431], [43.987, 174.618], [43.756, 174.764], [37.576, 176.79], [34.829, 180.67]], "c": true}]}, {"i": {"x": 1, "y": 1}, "o": {"x": 0.024, "y": 0}, "t": 97, "s": [{"i": [[0.235, 0.035], [0, 0], [0.058, 0.143], [0.002, 0.005], [0.004, 0.016], [0.002, 0.023], [0.021, 0.117], [0, 0.016], [0.012, 0.087], [0.006, 0.054], [0.012, 0.089], [0.001, 0.009], [-0.058, 0.762], [-0.001, 0.012], [-0.462, 1.321], [-0.029, 0.072], [-0.076, 0.183], [-0.029, 0.069], [-0.142, 0.3], [-0.153, 0.285], [-0.316, 0.484], [-0.828, 0.902], [-0.625, 0.545], [-0.255, 0.201], [-0.26, 0.186], [-0.132, 0.089], [-0.267, 0.163], [-0.27, 0.148], [-2.414, -0.151], [-0.103, -0.009], [-0.019, -0.003], [-0.088, -0.011], [-0.047, -0.008], [-0.014, -0.003], [-0.005, -0.002], [-0.039, -0.169], [-0.019, -0.105], [-0.002, -0.007], [-0.015, -0.087], [-0.015, -0.094], [-0.008, -0.075], [-0.006, -0.059], [-0.002, -0.033], [-0.008, -0.096], [-0.004, -0.156], [-0.001, -0.079], [0.033, -0.391], [0.003, -0.012], [0.014, -0.133], [0.021, -0.14], [0.231, 0.014], [3.874, -4.228], [0.634, -3.082]], "o": [[0, 0], [-0.154, -0.024], [-0.002, -0.005], [-0.007, -0.016], [-0.004, -0.021], [-0.026, -0.115], [-0.004, -0.014], [-0.017, -0.087], [-0.011, -0.052], [-0.012, -0.089], [-0.002, -0.007], [-0.083, -0.715], [-0.002, -0.014], [0.093, -1.265], [0.025, -0.074], [0.064, -0.183], [0.025, -0.072], [0.124, -0.299], [0.136, -0.289], [0.266, -0.494], [0.633, -0.971], [0.585, -0.638], [0.251, -0.219], [0.258, -0.203], [0.132, -0.093], [0.264, -0.179], [0.267, -0.163], [2.594, -1.418], [0.105, 0.004], [0.021, 0.003], [0.088, 0.009], [0.047, 0.006], [0.014, 0], [0.005, 0], [0.17, 0.031], [0.024, 0.103], [0.002, 0.005], [0.017, 0.084], [0.015, 0.091], [0.01, 0.073], [0.008, 0.058], [0.004, 0.035], [0.01, 0.094], [0.009, 0.152], [0.003, 0.077], [0.005, 0.38], [0, 0.012], [-0.009, 0.13], [-0.017, 0.14], [-0.032, 0.228], [-4.168, -0.255], [-2.601, 2.838], [-0.049, 0.235]], "v": [[42.412, 172.927], [42.403, 172.927], [42.06, 172.657], [42.056, 172.643], [42.04, 172.594], [42.028, 172.529], [41.955, 172.18], [41.946, 172.133], [41.902, 171.873], [41.877, 171.714], [41.842, 171.45], [41.84, 171.424], [41.801, 169.203], [41.804, 169.163], [42.642, 165.262], [42.721, 165.044], [42.931, 164.495], [43.014, 164.285], [43.418, 163.389], [43.853, 162.529], [44.727, 161.062], [46.919, 158.244], [48.74, 156.474], [49.503, 155.844], [50.28, 155.261], [50.675, 154.986], [51.47, 154.473], [52.276, 154.007], [59.976, 152.066], [60.286, 152.088], [60.344, 152.094], [60.61, 152.124], [60.749, 152.143], [60.794, 152.149], [60.81, 152.154], [61.156, 152.482], [61.223, 152.791], [61.227, 152.81], [61.276, 153.067], [61.32, 153.346], [61.349, 153.568], [61.369, 153.745], [61.379, 153.848], [61.404, 154.133], [61.426, 154.595], [61.431, 154.829], [61.389, 155.985], [61.386, 156.02], [61.348, 156.413], [61.294, 156.832], [60.828, 157.216], [47.789, 163.394], [42.916, 172.573]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.033, "y": 0.01}, "t": 102, "s": [{"i": [[0.387, -0.011], [0, 0], [0.134, 0.214], [0.005, 0.007], [0.012, 0.025], [0.01, 0.037], [0.067, 0.182], [0.004, 0.026], [0.044, 0.135], [0.025, 0.085], [0.045, 0.139], [0.004, 0.014], [0.121, 1.238], [0.002, 0.019], [-0.37, 2.25], [-0.027, 0.123], [-0.07, 0.315], [-0.027, 0.12], [-0.143, 0.522], [-0.166, 0.5], [-0.371, 0.866], [-1.074, 1.681], [-0.85, 1.05], [-0.353, 0.394], [-0.364, 0.372], [-0.186, 0.18], [-0.382, 0.337], [-0.392, 0.313], [-3.915, 0.438], [-0.167, 0.015], [-0.031, 0.001], [-0.145, 0.007], [-0.077, 0], [-0.023, 0], [-0.008, -0.003], [-0.11, -0.26], [-0.06, -0.164], [-0.005, -0.011], [-0.048, -0.135], [-0.05, -0.146], [-0.034, -0.118], [-0.026, -0.092], [-0.012, -0.052], [-0.039, -0.151], [-0.051, -0.25], [-0.024, -0.127], [-0.058, -0.637], [0.001, -0.019], [-0.015, -0.217], [-0.005, -0.23], [0.374, -0.042], [5.025, -7.875], [0.15, -5.125]], "o": [[0, 0], [-0.253, 0.005], [-0.005, -0.007], [-0.016, -0.025], [-0.013, -0.033], [-0.074, -0.177], [-0.011, -0.021], [-0.052, -0.134], [-0.032, -0.08], [-0.045, -0.139], [-0.006, -0.011], [-0.334, -1.124], [-0.007, -0.022], [-0.207, -2.056], [0.019, -0.126], [0.051, -0.312], [0.019, -0.122], [0.115, -0.514], [0.136, -0.502], [0.287, -0.868], [0.743, -1.736], [0.759, -1.188], [0.341, -0.423], [0.357, -0.398], [0.186, -0.187], [0.374, -0.362], [0.382, -0.337], [3.762, -3.006], [0.169, -0.023], [0.034, -0.001], [0.144, -0.011], [0.076, -0.004], [0.022, -0.004], [0.007, -0.001], [0.281, 0.002], [0.067, 0.159], [0.005, 0.007], [0.051, 0.13], [0.049, 0.142], [0.037, 0.113], [0.03, 0.092], [0.016, 0.055], [0.042, 0.147], [0.057, 0.241], [0.027, 0.123], [0.115, 0.609], [0.003, 0.019], [0.022, 0.212], [0.013, 0.229], [0.013, 0.375], [-6.759, 0.765], [-3.373, 5.287], [-0.012, 0.39]], "v": [[52.022, 162.337], [52.007, 162.339], [51.381, 162.003], [51.37, 161.981], [51.331, 161.907], [51.292, 161.805], [51.077, 161.266], [51.05, 161.193], [50.906, 160.789], [50.821, 160.541], [50.69, 160.127], [50.68, 160.086], [49.991, 156.533], [49.985, 156.469], [50.231, 149.974], [50.295, 149.602], [50.478, 148.662], [50.552, 148.301], [50.947, 146.75], [51.404, 145.248], [52.391, 142.647], [55.115, 137.509], [57.538, 134.156], [58.583, 132.93], [59.667, 131.776], [60.223, 131.223], [61.354, 130.176], [62.516, 129.202], [74.323, 123.918], [74.826, 123.866], [74.921, 123.859], [75.356, 123.833], [75.585, 123.824], [75.658, 123.82], [75.685, 123.823], [76.333, 124.252], [76.527, 124.729], [76.539, 124.758], [76.69, 125.157], [76.839, 125.592], [76.948, 125.94], [77.03, 126.219], [77.075, 126.381], [77.196, 126.832], [77.361, 127.567], [77.435, 127.94], [77.693, 129.807], [77.699, 129.864], [77.749, 130.506], [77.78, 131.193], [77.14, 131.939], [57.962, 145.526], [52.729, 161.626]], "c": true}]}, {"i": {"x": 0.92, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 105, "s": [{"i": [[0.47, -0.061], [0, 0], [0.19, 0.244], [0.007, 0.008], [0.017, 0.029], [0.016, 0.044], [0.104, 0.213], [0.008, 0.031], [0.071, 0.16], [0.041, 0.1], [0.072, 0.164], [0.007, 0.017], [0.303, 1.495], [0.005, 0.023], [-0.169, 2.79], [-0.017, 0.154], [-0.046, 0.393], [-0.018, 0.149], [-0.109, 0.654], [-0.139, 0.63], [-0.344, 1.103], [-1.099, 2.184], [-0.904, 1.387], [-0.381, 0.525], [-0.398, 0.5], [-0.205, 0.243], [-0.423, 0.458], [-0.439, 0.431], [-4.719, 1.024], [-0.202, 0.039], [-0.037, 0.005], [-0.176, 0.027], [-0.094, 0.01], [-0.028, 0.003], [-0.01, -0.002], [-0.167, -0.303], [-0.094, -0.192], [-0.008, -0.012], [-0.076, -0.158], [-0.079, -0.172], [-0.056, -0.139], [-0.043, -0.109], [-0.021, -0.062], [-0.067, -0.18], [-0.093, -0.298], [-0.045, -0.152], [-0.15, -0.77], [-0.001, -0.024], [-0.045, -0.263], [-0.035, -0.28], [0.451, -0.098], [5.142, -10.231], [-0.457, -6.269]], "o": [[0, 0], [-0.308, 0.038], [-0.007, -0.008], [-0.022, -0.028], [-0.02, -0.038], [-0.112, -0.206], [-0.016, -0.025], [-0.08, -0.157], [-0.049, -0.093], [-0.072, -0.164], [-0.008, -0.012], [-0.548, -1.329], [-0.012, -0.026], [-0.51, -2.481], [0.007, -0.156], [0.023, -0.387], [0.008, -0.152], [0.076, -0.641], [0.104, -0.629], [0.241, -1.094], [0.688, -2.209], [0.777, -1.544], [0.363, -0.558], [0.385, -0.53], [0.203, -0.251], [0.41, -0.489], [0.423, -0.458], [4.212, -4.136], [0.204, -0.049], [0.042, -0.006], [0.175, -0.032], [0.092, -0.015], [0.027, -0.007], [0.009, -0.002], [0.343, -0.033], [0.102, 0.185], [0.007, 0.008], [0.079, 0.153], [0.078, 0.167], [0.059, 0.134], [0.048, 0.108], [0.027, 0.065], [0.07, 0.174], [0.1, 0.287], [0.048, 0.146], [0.216, 0.728], [0.006, 0.023], [0.053, 0.256], [0.044, 0.277], [0.062, 0.455], [-8.147, 1.779], [-3.452, 6.868], [0.034, 0.477]], "v": [[56.849, 156.698], [56.831, 156.703], [56.026, 156.371], [56.01, 156.347], [55.953, 156.261], [55.893, 156.142], [55.563, 155.511], [55.521, 155.426], [55.295, 154.951], [55.16, 154.659], [54.949, 154.17], [54.932, 154.122], [53.648, 149.876], [53.632, 149.798], [53.119, 141.847], [53.151, 141.386], [53.257, 140.216], [53.302, 139.766], [53.589, 137.826], [53.958, 135.938], [54.837, 132.642], [57.687, 126.19], [60.222, 121.798], [61.344, 120.173], [62.522, 118.629], [63.13, 117.886], [64.379, 116.468], [65.674, 115.134], [79.669, 107.167], [80.277, 107.04], [80.392, 107.02], [80.918, 106.934], [81.196, 106.894], [81.284, 106.88], [81.318, 106.881], [82.162, 107.323], [82.458, 107.881], [82.477, 107.914], [82.711, 108.382], [82.947, 108.893], [83.123, 109.304], [83.258, 109.635], [83.333, 109.827], [83.537, 110.361], [83.83, 111.237], [83.967, 111.682], [84.516, 113.927], [84.529, 113.995], [84.671, 114.772], [84.795, 115.606], [84.108, 116.596], [61.99, 135.456], [57.623, 155.743]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.047, "y": 0}, "t": 108, "s": [{"i": [[0.547, -0.108], [0, 0], [0.241, 0.271], [0.009, 0.009], [0.023, 0.033], [0.022, 0.05], [0.139, 0.242], [0.012, 0.036], [0.095, 0.182], [0.055, 0.114], [0.097, 0.187], [0.01, 0.02], [0.469, 1.73], [0.007, 0.027], [0.014, 3.284], [-0.008, 0.182], [-0.024, 0.464], [-0.009, 0.176], [-0.077, 0.775], [-0.115, 0.75], [-0.319, 1.319], [-1.123, 2.644], [-0.954, 1.695], [-0.407, 0.645], [-0.428, 0.616], [-0.221, 0.301], [-0.461, 0.57], [-0.482, 0.539], [-5.456, 1.56], [-0.234, 0.061], [-0.043, 0.009], [-0.204, 0.045], [-0.109, 0.019], [-0.033, 0.005], [-0.012, -0.002], [-0.218, -0.342], [-0.125, -0.218], [-0.01, -0.014], [-0.101, -0.18], [-0.106, -0.196], [-0.076, -0.159], [-0.059, -0.125], [-0.029, -0.071], [-0.092, -0.206], [-0.132, -0.342], [-0.064, -0.175], [-0.235, -0.891], [-0.004, -0.028], [-0.073, -0.304], [-0.063, -0.325], [0.521, -0.15], [5.25, -12.388], [-1.012, -7.317]], "o": [[0, 0], [-0.358, 0.068], [-0.009, -0.009], [-0.028, -0.031], [-0.026, -0.043], [-0.147, -0.233], [-0.021, -0.028], [-0.106, -0.178], [-0.064, -0.105], [-0.097, -0.187], [-0.01, -0.014], [-0.744, -1.517], [-0.016, -0.029], [-0.787, -2.87], [-0.004, -0.183], [-0.002, -0.455], [-0.002, -0.178], [0.041, -0.758], [0.074, -0.746], [0.199, -1.301], [0.639, -2.643], [0.794, -1.87], [0.383, -0.682], [0.411, -0.651], [0.219, -0.31], [0.444, -0.604], [0.461, -0.57], [4.623, -5.17], [0.235, -0.073], [0.048, -0.01], [0.202, -0.05], [0.107, -0.024], [0.031, -0.01], [0.01, -0.003], [0.4, -0.064], [0.133, 0.209], [0.009, 0.009], [0.104, 0.173], [0.104, 0.19], [0.08, 0.152], [0.064, 0.123], [0.036, 0.074], [0.095, 0.199], [0.139, 0.329], [0.068, 0.168], [0.309, 0.837], [0.009, 0.026], [0.082, 0.296], [0.073, 0.322], [0.108, 0.529], [-9.417, 2.706], [-3.524, 8.317], [0.077, 0.557]], "v": [[60.48, 150.597], [60.459, 150.604], [59.49, 150.277], [59.469, 150.249], [59.396, 150.152], [59.316, 150.017], [58.881, 149.303], [58.826, 149.206], [58.524, 148.667], [58.344, 148.335], [58.059, 147.778], [58.035, 147.722], [56.206, 142.841], [56.182, 142.751], [54.974, 133.468], [54.977, 132.924], [55.011, 131.545], [55.03, 131.014], [55.219, 128.717], [55.508, 126.475], [56.287, 122.544], [59.253, 114.888], [61.891, 109.546], [63.083, 107.554], [64.346, 105.655], [65.002, 104.737], [66.359, 102.979], [67.776, 101.316], [83.775, 90.893], [84.477, 90.698], [84.61, 90.665], [85.221, 90.525], [85.544, 90.457], [85.646, 90.434], [85.686, 90.432], [86.709, 90.886], [87.099, 91.518], [87.124, 91.556], [87.434, 92.086], [87.75, 92.668], [87.987, 93.136], [88.171, 93.513], [88.273, 93.733], [88.553, 94.343], [88.964, 95.348], [89.158, 95.86], [89.973, 98.45], [89.993, 98.529], [90.219, 99.429], [90.428, 100.398], [89.698, 101.611], [64.889, 125.298], [61.314, 149.418]], "c": true}]}, {"i": {"x": 0.88, "y": 1}, "o": {"x": 0, "y": 0}, "t": 112, "s": [{"i": [[0.643, -0.171], [0, 0], [0.308, 0.305], [0.011, 0.01], [0.029, 0.037], [0.031, 0.058], [0.184, 0.278], [0.017, 0.042], [0.128, 0.209], [0.075, 0.132], [0.13, 0.215], [0.013, 0.023], [0.695, 2.026], [0.011, 0.032], [0.274, 3.915], [0.004, 0.217], [0.008, 0.555], [0.003, 0.211], [-0.031, 0.93], [-0.078, 0.903], [-0.277, 1.598], [-1.132, 3.241], [-1.004, 2.097], [-0.435, 0.801], [-0.463, 0.769], [-0.24, 0.376], [-0.506, 0.716], [-0.532, 0.68], [-6.384, 2.287], [-0.274, 0.091], [-0.051, 0.014], [-0.24, 0.07], [-0.129, 0.031], [-0.039, 0.009], [-0.015, -0.001], [-0.287, -0.391], [-0.166, -0.25], [-0.014, -0.016], [-0.134, -0.207], [-0.141, -0.225], [-0.104, -0.184], [-0.08, -0.144], [-0.041, -0.082], [-0.126, -0.238], [-0.184, -0.398], [-0.09, -0.203], [-0.35, -1.044], [-0.006, -0.033], [-0.111, -0.357], [-0.1, -0.383], [0.61, -0.219], [5.29, -15.184], [-1.778, -8.647]], "o": [[0, 0], [-0.422, 0.109], [-0.011, -0.01], [-0.036, -0.035], [-0.034, -0.05], [-0.194, -0.267], [-0.027, -0.031], [-0.14, -0.204], [-0.085, -0.121], [-0.13, -0.215], [-0.014, -0.016], [-1.006, -1.75], [-0.021, -0.034], [-1.163, -3.361], [-0.019, -0.218], [-0.038, -0.542], [-0.016, -0.212], [-0.011, -0.907], [0.029, -0.895], [0.136, -1.568], [0.555, -3.202], [0.8, -2.292], [0.404, -0.844], [0.439, -0.809], [0.237, -0.387], [0.482, -0.755], [0.506, -0.716], [5.108, -6.528], [0.275, -0.105], [0.057, -0.016], [0.237, -0.076], [0.126, -0.038], [0.036, -0.015], [0.012, -0.005], [0.472, -0.108], [0.175, 0.239], [0.011, 0.01], [0.138, 0.198], [0.139, 0.219], [0.107, 0.175], [0.086, 0.141], [0.049, 0.086], [0.129, 0.23], [0.191, 0.381], [0.094, 0.195], [0.434, 0.974], [0.012, 0.03], [0.121, 0.346], [0.112, 0.378], [0.17, 0.623], [-11.018, 3.965], [-3.551, 10.193], [0.135, 0.658]], "v": [[63.984, 144.437], [63.96, 144.447], [62.778, 144.132], [62.751, 144.1], [62.657, 143.991], [62.551, 143.836], [61.976, 143.019], [61.903, 142.907], [61.501, 142.288], [61.26, 141.906], [60.877, 141.264], [60.843, 141.2], [58.28, 135.523], [58.244, 135.417], [56.077, 124.441], [56.037, 123.793], [55.971, 122.146], [55.951, 121.511], [55.998, 118.757], [56.166, 116.06], [56.787, 111.312], [59.309, 101.625], [62.037, 95.047], [63.302, 92.579], [64.659, 90.215], [65.37, 89.069], [66.85, 86.867], [68.409, 84.773], [86.087, 71.236], [86.909, 70.949], [87.066, 70.899], [87.783, 70.683], [88.163, 70.577], [88.283, 70.542], [88.33, 70.537], [89.586, 70.998], [90.101, 71.72], [90.132, 71.764], [90.544, 72.373], [90.966, 73.041], [91.286, 73.581], [91.535, 74.016], [91.674, 74.27], [92.055, 74.976], [92.624, 76.142], [92.896, 76.738], [94.07, 79.762], [94.101, 79.855], [94.441, 80.91], [94.765, 82.049], [93.989, 83.553], [67.26, 113.922], [64.887, 142.965]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.037, "y": 0}, "t": 119, "s": [{"i": [[0.725, -0.307], [0, 0], [0.409, 0.312], [0.014, 0.01], [0.04, 0.04], [0.045, 0.064], [0.26, 0.3], [0.027, 0.047], [0.183, 0.228], [0.109, 0.144], [0.187, 0.234], [0.019, 0.025], [1.134, 2.292], [0.013, 0.038], [0.941, 4.601], [0.039, 0.257], [0.097, 0.657], [0.036, 0.25], [0.112, 1.108], [0.052, 1.084], [-0.071, 1.941], [-0.81, 4.027], [-0.841, 2.649], [-0.382, 1.02], [-0.419, 0.986], [-0.221, 0.485], [-0.478, 0.93], [-0.515, 0.893], [-7.103, 3.741], [-0.306, 0.152], [-0.057, 0.025], [-0.269, 0.121], [-0.145, 0.058], [-0.044, 0.017], [-0.017, 0.001], [-0.398, -0.418], [-0.234, -0.27], [-0.018, -0.016], [-0.19, -0.224], [-0.201, -0.244], [-0.15, -0.201], [-0.116, -0.158], [-0.061, -0.091], [-0.185, -0.262], [-0.278, -0.443], [-0.138, -0.226], [-0.574, -1.182], [-0.013, -0.038], [-0.186, -0.406], [-0.178, -0.438], [0.678, -0.358], [3.779, -18.865], [-3.449, -9.973]], "o": [[0, 0], [-0.476, 0.197], [-0.014, -0.01], [-0.047, -0.036], [-0.048, -0.053], [-0.269, -0.285], [-0.037, -0.033], [-0.196, -0.22], [-0.118, -0.13], [-0.187, -0.234], [-0.018, -0.017], [-1.454, -1.915], [-0.03, -0.037], [-1.893, -3.8], [-0.057, -0.256], [-0.131, -0.637], [-0.053, -0.249], [-0.157, -1.074], [-0.108, -1.067], [-0.09, -1.882], [0.141, -3.888], [0.572, -2.848], [0.338, -1.066], [0.385, -1.03], [0.216, -0.497], [0.444, -0.974], [0.478, -0.93], [4.939, -8.566], [0.305, -0.169], [0.064, -0.028], [0.266, -0.128], [0.141, -0.065], [0.04, -0.023], [0.013, -0.008], [0.534, -0.204], [0.243, 0.255], [0.014, 0.01], [0.192, 0.213], [0.197, 0.237], [0.153, 0.191], [0.123, 0.154], [0.071, 0.094], [0.188, 0.252], [0.284, 0.421], [0.141, 0.216], [0.662, 1.086], [0.019, 0.034], [0.196, 0.392], [0.191, 0.43], [0.298, 0.712], [-12.256, 6.476], [-2.537, 12.665], [0.262, 0.759]], "v": [[69.562, 133.564], [69.535, 133.58], [68.103, 133.397], [68.067, 133.364], [67.939, 133.249], [67.791, 133.082], [66.989, 132.205], [66.885, 132.085], [66.317, 131.414], [65.974, 131], [65.425, 130.3], [65.376, 130.23], [61.478, 123.906], [61.419, 123.787], [57.144, 111.113], [56.995, 110.351], [56.656, 108.407], [56.533, 107.657], [56.15, 104.383], [55.919, 101.156], [55.894, 95.423], [57.307, 83.524], [59.453, 75.281], [60.542, 72.15], [61.754, 69.127], [62.404, 67.653], [63.785, 64.802], [65.277, 62.067], [83.805, 43.163], [84.721, 42.69], [84.896, 42.606], [85.7, 42.235], [86.128, 42.048], [86.263, 41.987], [86.317, 41.973], [87.859, 42.318], [88.575, 43.092], [88.619, 43.139], [89.197, 43.795], [89.797, 44.52], [90.256, 45.109], [90.616, 45.585], [90.819, 45.864], [91.377, 46.641], [92.227, 47.933], [92.639, 48.595], [94.492, 51.994], [94.543, 52.1], [95.108, 53.297], [95.668, 54.596], [94.999, 56.506], [68.555, 96.835], [70.384, 131.673]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 124, "s": [{"i": [[0.791, -0.379], [0, 0], [0.476, 0.315], [0.017, 0.01], [0.048, 0.041], [0.055, 0.067], [0.309, 0.311], [0.033, 0.05], [0.218, 0.237], [0.13, 0.151], [0.223, 0.244], [0.023, 0.026], [1.409, 2.428], [0.017, 0.04], [1.337, 4.955], [0.06, 0.278], [0.15, 0.71], [0.056, 0.27], [0.194, 1.201], [0.125, 1.178], [0.042, 2.119], [-0.653, 4.438], [-0.774, 2.939], [-0.363, 1.135], [-0.406, 1.101], [-0.217, 0.542], [-0.476, 1.043], [-0.519, 1.005], [-7.697, 4.515], [-0.332, 0.184], [-0.063, 0.03], [-0.293, 0.149], [-0.159, 0.072], [-0.048, 0.021], [-0.019, 0.002], [-0.47, -0.431], [-0.278, -0.28], [-0.021, -0.017], [-0.225, -0.232], [-0.24, -0.253], [-0.18, -0.21], [-0.14, -0.165], [-0.073, -0.095], [-0.223, -0.275], [-0.338, -0.465], [-0.168, -0.238], [-0.715, -1.253], [-0.017, -0.04], [-0.233, -0.431], [-0.226, -0.467], [0.735, -0.432], [3.045, -20.791], [-4.471, -10.654]], "o": [[0, 0], [-0.519, 0.244], [-0.017, -0.01], [-0.055, -0.036], [-0.057, -0.055], [-0.318, -0.294], [-0.043, -0.034], [-0.233, -0.227], [-0.14, -0.134], [-0.223, -0.244], [-0.022, -0.017], [-1.743, -1.997], [-0.036, -0.038], [-2.35, -4.024], [-0.079, -0.276], [-0.186, -0.686], [-0.074, -0.269], [-0.242, -1.161], [-0.187, -1.156], [-0.217, -2.045], [-0.084, -4.245], [0.462, -3.138], [0.311, -1.182], [0.366, -1.146], [0.21, -0.555], [0.435, -1.089], [0.476, -1.043], [4.98, -9.639], [0.33, -0.203], [0.07, -0.035], [0.288, -0.156], [0.154, -0.079], [0.043, -0.028], [0.014, -0.009], [0.584, -0.255], [0.287, 0.263], [0.017, 0.01], [0.228, 0.22], [0.235, 0.246], [0.183, 0.198], [0.147, 0.16], [0.085, 0.098], [0.225, 0.263], [0.343, 0.441], [0.17, 0.227], [0.807, 1.142], [0.024, 0.036], [0.243, 0.415], [0.24, 0.457], [0.377, 0.757], [-13.28, 7.814], [-2.044, 13.957], [0.34, 0.811]], "v": [[71.083, 127.336], [71.054, 127.355], [69.445, 127.243], [69.402, 127.21], [69.252, 127.092], [69.076, 126.92], [68.126, 126.013], [68.003, 125.889], [67.327, 125.193], [66.919, 124.763], [66.262, 124.034], [66.202, 123.96], [61.457, 117.311], [61.384, 117.185], [55.822, 103.638], [55.609, 102.816], [55.109, 100.719], [54.925, 99.909], [54.294, 96.365], [53.835, 92.863], [53.449, 86.617], [54.286, 73.564], [56.17, 64.45], [57.19, 60.97], [58.355, 57.601], [58.989, 55.955], [60.354, 52.763], [61.849, 49.691], [81.357, 27.947], [82.35, 27.375], [82.54, 27.272], [83.415, 26.818], [83.882, 26.588], [84.028, 26.513], [84.088, 26.495], [85.831, 26.776], [86.679, 27.575], [86.731, 27.623], [87.417, 28.302], [88.131, 29.055], [88.681, 29.669], [89.113, 30.165], [89.357, 30.457], [90.027, 31.268], [91.057, 32.624], [91.559, 33.32], [93.838, 36.91], [93.902, 37.021], [94.607, 38.291], [95.314, 39.672], [94.685, 41.794], [67.673, 87.375], [71.884, 125.224]], "c": true}]}, {"i": {"x": 0.245, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 131, "s": [{"i": [[0.819, -0.469], [0, 0], [0.534, 0.304], [0.019, 0.009], [0.054, 0.04], [0.064, 0.068], [0.354, 0.313], [0.039, 0.052], [0.252, 0.239], [0.151, 0.153], [0.257, 0.247], [0.023, 0.029], [1.694, 2.518], [0.021, 0.043], [1.809, 5.256], [0.085, 0.296], [0.214, 0.756], [0.081, 0.288], [0.299, 1.283], [0.224, 1.264], [0.206, 2.288], [-0.363, 4.847], [-0.607, 3.236], [-0.302, 1.254], [-0.352, 1.22], [-0.191, 0.602], [-0.43, 1.164], [-0.48, 1.125], [-7.906, 5.461], [-0.342, 0.224], [-0.065, 0.037], [-0.303, 0.183], [-0.165, 0.09], [-0.05, 0.026], [-0.02, 0.004], [-0.537, -0.43], [-0.319, -0.282], [-0.024, -0.017], [-0.259, -0.234], [-0.276, -0.256], [-0.209, -0.214], [-0.162, -0.168], [-0.086, -0.098], [-0.26, -0.28], [-0.398, -0.477], [-0.198, -0.245], [-0.861, -1.301], [-0.021, -0.042], [-0.283, -0.448], [-0.277, -0.487], [0.755, -0.523], [1.685, -22.706], [-5.599, -11.181]], "o": [[0, 0], [-0.538, 0.303], [-0.019, -0.009], [-0.061, -0.035], [-0.065, -0.055], [-0.363, -0.294], [-0.048, -0.033], [-0.267, -0.228], [-0.16, -0.134], [-0.257, -0.247], [-0.024, -0.017], [-2.019, -2.027], [-0.041, -0.039], [-2.824, -4.173], [-0.106, -0.292], [-0.251, -0.728], [-0.1, -0.285], [-0.347, -1.237], [-0.288, -1.235], [-0.388, -2.194], [-0.412, -4.583], [0.257, -3.428], [0.244, -1.302], [0.305, -1.267], [0.183, -0.616], [0.384, -1.21], [0.43, -1.164], [4.606, -10.796], [0.338, -0.245], [0.072, -0.043], [0.297, -0.191], [0.159, -0.097], [0.044, -0.034], [0.015, -0.011], [0.606, -0.32], [0.328, 0.263], [0.019, 0.009], [0.261, 0.221], [0.27, 0.248], [0.211, 0.201], [0.17, 0.162], [0.099, 0.1], [0.261, 0.267], [0.401, 0.451], [0.2, 0.232], [0.951, 1.174], [0.028, 0.037], [0.292, 0.43], [0.292, 0.476], [0.461, 0.79], [-13.639, 9.448], [-1.131, 15.243], [0.426, 0.851]], "v": [[73.269, 121.142], [73.239, 121.165], [71.506, 121.166], [71.457, 121.132], [71.288, 121.017], [71.086, 120.844], [70, 119.935], [69.858, 119.81], [69.08, 119.109], [68.61, 118.675], [67.851, 117.937], [67.782, 117.861], [62.191, 111.03], [62.103, 110.899], [55.115, 96.673], [54.824, 95.801], [54.13, 93.571], [53.87, 92.71], [52.925, 88.926], [52.167, 85.175], [51.28, 78.451], [51.187, 64.277], [52.513, 54.282], [53.343, 50.443], [54.336, 46.713], [54.89, 44.886], [56.11, 41.332], [57.48, 37.898], [76.735, 12.923], [77.757, 12.23], [77.952, 12.105], [78.856, 11.548], [79.338, 11.263], [79.489, 11.172], [79.552, 11.147], [81.441, 11.32], [82.41, 12.12], [82.47, 12.167], [83.257, 12.85], [84.079, 13.61], [84.715, 14.232], [85.216, 14.737], [85.499, 15.033], [86.279, 15.861], [87.486, 17.248], [88.076, 17.963], [90.792, 21.672], [90.869, 21.788], [91.72, 23.107], [92.582, 24.547], [92.07, 26.889], [66.581, 78.199], [73.966, 118.799]], "c": true}]}, {"t": 150, "s": [{"i": [[0.862, -0.542], [0, 0], [0.588, 0.305], [0.02, 0.009], [0.06, 0.041], [0.071, 0.071], [0.395, 0.322], [0.044, 0.054], [0.282, 0.248], [0.169, 0.158], [0.288, 0.255], [0.025, 0.031], [1.932, 2.645], [0.024, 0.045], [2.173, 5.595], [0.105, 0.316], [0.263, 0.807], [0.099, 0.307], [0.377, 1.372], [0.295, 1.355], [0.319, 2.46], [-0.186, 5.246], [-0.516, 3.518], [-0.273, 1.366], [-0.328, 1.332], [-0.18, 0.658], [-0.415, 1.274], [-0.47, 1.235], [-8.289, 6.231], [-0.359, 0.257], [-0.068, 0.043], [-0.319, 0.211], [-0.174, 0.104], [-0.053, 0.03], [-0.022, 0.005], [-0.597, -0.441], [-0.356, -0.29], [-0.027, -0.017], [-0.289, -0.241], [-0.308, -0.264], [-0.234, -0.221], [-0.182, -0.174], [-0.097, -0.102], [-0.292, -0.291], [-0.449, -0.498], [-0.224, -0.256], [-0.983, -1.367], [-0.024, -0.045], [-0.324, -0.472], [-0.32, -0.514], [0.791, -0.596], [0.853, -24.574], [-6.508, -11.826]], "o": [[0, 0], [-0.567, 0.35], [-0.02, -0.009], [-0.068, -0.035], [-0.073, -0.057], [-0.404, -0.302], [-0.054, -0.034], [-0.297, -0.235], [-0.178, -0.138], [-0.288, -0.255], [-0.027, -0.017], [-2.262, -2.101], [-0.046, -0.04], [-3.221, -4.382], [-0.126, -0.311], [-0.301, -0.775], [-0.12, -0.303], [-0.426, -1.32], [-0.363, -1.321], [-0.511, -2.351], [-0.639, -4.928], [0.131, -3.71], [0.208, -1.415], [0.275, -1.38], [0.172, -0.673], [0.362, -1.322], [0.415, -1.274], [4.506, -11.847], [0.354, -0.278], [0.076, -0.049], [0.312, -0.218], [0.167, -0.112], [0.046, -0.038], [0.015, -0.013], [0.64, -0.371], [0.365, 0.27], [0.02, 0.009], [0.291, 0.227], [0.302, 0.256], [0.236, 0.208], [0.19, 0.168], [0.111, 0.103], [0.293, 0.277], [0.452, 0.47], [0.225, 0.242], [1.075, 1.226], [0.032, 0.039], [0.333, 0.451], [0.335, 0.501], [0.53, 0.833], [-14.299, 10.779], [-0.572, 16.497], [0.495, 0.9]], "v": [[72.363, 114.873], [72.332, 114.898], [70.464, 114.973], [70.41, 114.939], [70.222, 114.822], [69.998, 114.644], [68.788, 113.71], [68.63, 113.581], [67.763, 112.858], [67.238, 112.409], [66.388, 111.645], [66.31, 111.567], [59.995, 104.434], [59.895, 104.297], [51.76, 89.245], [51.409, 88.316], [50.567, 85.94], [50.25, 85.022], [49.071, 80.979], [48.096, 76.963], [46.854, 69.746], [46.152, 54.454], [47.158, 43.611], [47.889, 39.434], [48.8, 35.366], [49.32, 33.37], [50.485, 29.483], [51.815, 25.719], [71.508, -2.058], [72.579, -2.849], [72.785, -2.993], [73.734, -3.632], [74.242, -3.96], [74.401, -4.065], [74.468, -4.094], [76.511, -3.989], [77.59, -3.167], [77.656, -3.118], [78.533, -2.415], [79.451, -1.63], [80.163, -0.986], [80.724, -0.463], [81.042, -0.155], [81.918, 0.704], [83.277, 2.149], [83.944, 2.896], [87.028, 6.782], [87.116, 6.904], [88.09, 8.291], [89.08, 9.808], [88.627, 12.357], [63.333, 68.818], [73.015, 112.314]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.173, 0.647, 0.553, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-1827-growing-plant').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [173.926, 203.102], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 50, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 1, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.898}, "o": {"x": 0.425, "y": 0}, "t": 89, "s": [{"i": [[0.217, -0.214], [0.005, -0.005], [0.005, 0.002], [0, 0], [0.002, 0.001], [0.007, 0.004], [0.001, 0.001], [0.005, 0.003], [0.003, 0.002], [0.005, 0.003], [0.001, 0], [0.04, 0.035], [0.001, 0.001], [0.054, 0.082], [0.003, 0.005], [0.007, 0.012], [0.003, 0.005], [0.01, 0.021], [0.009, 0.021], [0.014, 0.038], [0.015, 0.084], [0.004, 0.058], [0, 0.023], [-0.001, 0.022], [-0.001, 0.011], [-0.002, 0.022], [-0.003, 0.021], [-0.11, 0.128], [-0.005, 0.005], [-0.001, 0.001], [-0.004, 0.004], [-0.002, 0.002], [-0.001, 0.001], [0, 0], [-0.011, -0.005], [-0.007, -0.003], [0, 0], [-0.005, -0.003], [-0.006, -0.003], [-0.005, -0.003], [-0.003, -0.002], [-0.002, -0.001], [-0.006, -0.004], [-0.009, -0.006], [-0.004, -0.003], [-0.01, -0.009], [-0.01, -0.009], [-0.001, -0.001], [-0.007, -0.006], [-0.007, -0.007], [-0.052, -0.288]], "o": [[-0.005, 0.005], [-0.005, -0.002], [0, 0], [-0.003, -0.001], [-0.007, -0.003], [-0.001, 0], [-0.006, -0.003], [-0.003, -0.002], [-0.005, -0.003], [0, 0], [-0.043, -0.026], [-0.001, 0], [-0.066, -0.059], [-0.003, -0.005], [-0.007, -0.011], [-0.003, -0.004], [-0.011, -0.02], [-0.01, -0.02], [-0.016, -0.036], [-0.027, -0.076], [-0.011, -0.06], [-0.001, -0.023], [0, -0.023], [0, -0.011], [0.001, -0.022], [0.002, -0.022], [0.031, -0.204], [0.005, -0.006], [0.001, -0.001], [0.004, -0.005], [0.002, -0.002], [0.001, -0.001], [0, 0], [0.009, -0.008], [0.007, 0.003], [0, 0], [0.005, 0.003], [0.006, 0.003], [0.004, 0.003], [0.004, 0.002], [0.002, 0.001], [0.006, 0.003], [0.009, 0.006], [0.004, 0.003], [0.011, 0.008], [0.01, 0.009], [0.001, 0.001], [0.007, 0.006], [0.007, 0.007], [0.165, 0.167], [0.076, 0.421]], "v": [[29.572, 186.411], [29.558, 186.425], [29.543, 186.419], [29.542, 186.419], [29.534, 186.415], [29.512, 186.405], [29.509, 186.403], [29.492, 186.395], [29.483, 186.389], [29.466, 186.38], [29.465, 186.379], [29.34, 186.287], [29.338, 186.286], [29.156, 186.074], [29.147, 186.061], [29.125, 186.026], [29.117, 186.012], [29.084, 185.952], [29.055, 185.891], [29.01, 185.781], [28.946, 185.54], [28.925, 185.364], [28.922, 185.295], [28.922, 185.227], [28.923, 185.193], [28.929, 185.127], [28.937, 185.063], [29.154, 184.552], [29.168, 184.536], [29.171, 184.533], [29.184, 184.519], [29.191, 184.512], [29.193, 184.51], [29.194, 184.51], [29.227, 184.504], [29.247, 184.513], [29.248, 184.514], [29.265, 184.522], [29.282, 184.531], [29.295, 184.539], [29.306, 184.546], [29.312, 184.549], [29.329, 184.56], [29.356, 184.578], [29.369, 184.588], [29.401, 184.613], [29.432, 184.639], [29.433, 184.641], [29.454, 184.66], [29.475, 184.68], [29.819, 185.382]], "c": true}]}, {"i": {"x": 0.88, "y": 1}, "o": {"x": 0.43, "y": 0.263}, "t": 93, "s": [{"i": [[1.877, 0.545], [0.041, 0.014], [0.002, 0.035], [0.001, 0.002], [0.001, 0.018], [0.002, 0.054], [-0.001, 0.007], [0, 0.04], [-0.001, 0.025], [0, 0.041], [0, 0.004], [-0.073, 0.343], [-0.001, 0.005], [-0.289, 0.573], [-0.018, 0.031], [-0.045, 0.079], [-0.017, 0.03], [-0.082, 0.128], [-0.086, 0.12], [-0.171, 0.201], [-0.425, 0.36], [-0.313, 0.209], [-0.127, 0.076], [-0.128, 0.069], [-0.064, 0.032], [-0.129, 0.058], [-0.129, 0.051], [-1.062, -0.214], [-0.045, -0.01], [-0.008, -0.002], [-0.039, -0.01], [-0.02, -0.006], [-0.006, -0.002], [-0.002, -0.001], [-0.007, -0.079], [-0.002, -0.049], [-0.001, -0.003], [-0.001, -0.04], [-0.001, -0.043], [0.001, -0.034], [0.001, -0.027], [0.001, -0.015], [0.003, -0.044], [0.008, -0.071], [0.005, -0.036], [0.015, -0.087], [0.02, -0.088], [0.002, -0.005], [0.015, -0.059], [0.018, -0.062], [1.453, -1.234]], "o": [[-0.043, -0.012], [-0.004, -0.035], [-0.001, -0.002], [-0.003, -0.018], [-0.004, -0.054], [-0.001, -0.007], [-0.002, -0.04], [-0.001, -0.024], [0, -0.041], [-0.001, -0.003], [0.009, -0.33], [0, -0.007], [0.123, -0.569], [0.016, -0.032], [0.04, -0.079], [0.016, -0.031], [0.074, -0.128], [0.079, -0.123], [0.149, -0.209], [0.343, -0.403], [0.3, -0.255], [0.125, -0.084], [0.127, -0.076], [0.065, -0.034], [0.129, -0.065], [0.129, -0.058], [1.241, -0.489], [0.046, 0.008], [0.009, 0.003], [0.039, 0.009], [0.02, 0.005], [0.006, 0.001], [0.002, 0], [0.073, 0.024], [0.004, 0.048], [0.001, 0.002], [0.002, 0.039], [0.001, 0.042], [0, 0.034], [0, 0.027], [0, 0.016], [-0.002, 0.043], [-0.006, 0.07], [-0.003, 0.035], [-0.011, 0.086], [-0.015, 0.087], [-0.001, 0.005], [-0.012, 0.059], [-0.016, 0.062], [-0.422, 1.48], [-2.125, 1.804]], "v": [[34.416, 180.802], [34.29, 180.764], [34.281, 180.659], [34.28, 180.652], [34.274, 180.598], [34.264, 180.436], [34.263, 180.414], [34.26, 180.293], [34.259, 180.219], [34.261, 180.097], [34.261, 180.085], [34.385, 179.073], [34.389, 179.056], [35.01, 177.333], [35.058, 177.238], [35.187, 177.001], [35.237, 176.911], [35.473, 176.528], [35.721, 176.163], [36.202, 175.549], [37.354, 174.399], [38.275, 173.704], [38.654, 173.464], [39.036, 173.245], [39.228, 173.144], [39.614, 172.959], [40.001, 172.795], [43.542, 172.375], [43.678, 172.404], [43.704, 172.41], [43.82, 172.44], [43.88, 172.457], [43.9, 172.462], [43.907, 172.465], [44.039, 172.635], [44.049, 172.779], [44.05, 172.788], [44.055, 172.908], [44.057, 173.037], [44.056, 173.14], [44.053, 173.222], [44.051, 173.269], [44.044, 173.4], [44.024, 173.612], [44.012, 173.718], [43.972, 173.978], [43.92, 174.241], [43.916, 174.257], [43.874, 174.433], [43.824, 174.62], [40.962, 178.865]], "c": true}]}, {"i": {"x": 1, "y": 1}, "o": {"x": 0.024, "y": 0}, "t": 97, "s": [{"i": [[4.32, 0.629], [0.095, 0.018], [0.015, 0.075], [0.002, 0.005], [0.009, 0.038], [0.021, 0.117], [0, 0.016], [0.012, 0.087], [0.006, 0.054], [0.012, 0.089], [0.001, 0.009], [-0.056, 0.762], [-0.001, 0.012], [-0.462, 1.321], [-0.029, 0.072], [-0.076, 0.183], [-0.029, 0.069], [-0.142, 0.3], [-0.153, 0.285], [-0.316, 0.484], [-0.828, 0.902], [-0.627, 0.542], [-0.258, 0.201], [-0.262, 0.186], [-0.132, 0.089], [-0.267, 0.163], [-0.27, 0.148], [-2.414, -0.151], [-0.103, -0.009], [-0.019, -0.003], [-0.088, -0.011], [-0.047, -0.008], [-0.014, -0.003], [-0.005, -0.002], [-0.039, -0.169], [-0.019, -0.105], [-0.002, -0.007], [-0.015, -0.087], [-0.015, -0.094], [-0.008, -0.075], [-0.006, -0.059], [-0.002, -0.033], [-0.008, -0.096], [-0.004, -0.156], [-0.001, -0.079], [0.007, -0.193], [0.016, -0.196], [0.003, -0.012], [0.014, -0.133], [0.02, -0.14], [2.832, -3.088]], "o": [[-0.098, -0.013], [-0.02, -0.075], [-0.002, -0.005], [-0.011, -0.038], [-0.026, -0.115], [-0.004, -0.014], [-0.017, -0.087], [-0.011, -0.052], [-0.012, -0.089], [-0.002, -0.007], [-0.083, -0.715], [-0.002, -0.014], [0.095, -1.265], [0.025, -0.074], [0.064, -0.183], [0.025, -0.072], [0.124, -0.299], [0.136, -0.289], [0.266, -0.494], [0.633, -0.971], [0.585, -0.638], [0.251, -0.217], [0.258, -0.201], [0.132, -0.093], [0.264, -0.177], [0.267, -0.163], [2.594, -1.418], [0.105, 0.004], [0.021, 0.003], [0.088, 0.009], [0.047, 0.005], [0.014, 0], [0.005, 0], [0.17, 0.031], [0.024, 0.103], [0.002, 0.005], [0.017, 0.084], [0.015, 0.091], [0.01, 0.073], [0.008, 0.058], [0.004, 0.035], [0.01, 0.094], [0.009, 0.152], [0.003, 0.077], [0.002, 0.189], [-0.007, 0.193], [0, 0.012], [-0.009, 0.13], [-0.016, 0.14], [-0.475, 3.32], [-4.14, 4.515]], "v": [[42.239, 172.93], [41.951, 172.885], [41.897, 172.66], [41.892, 172.646], [41.864, 172.531], [41.791, 172.182], [41.783, 172.135], [41.738, 171.875], [41.713, 171.716], [41.678, 171.452], [41.676, 171.427], [41.637, 169.205], [41.64, 169.166], [42.478, 165.265], [42.557, 165.047], [42.768, 164.498], [42.851, 164.287], [43.254, 163.392], [43.69, 162.532], [44.563, 161.064], [46.755, 158.246], [48.576, 156.476], [49.339, 155.846], [50.117, 155.263], [50.511, 154.988], [51.307, 154.476], [52.113, 154.01], [59.813, 152.069], [60.123, 152.09], [60.181, 152.096], [60.446, 152.127], [60.586, 152.146], [60.63, 152.151], [60.646, 152.156], [60.992, 152.484], [61.059, 152.793], [61.063, 152.812], [61.112, 153.069], [61.156, 153.348], [61.185, 153.57], [61.205, 153.748], [61.215, 153.85], [61.24, 154.135], [61.262, 154.598], [61.267, 154.831], [61.26, 155.404], [61.225, 155.987], [61.222, 156.022], [61.185, 156.416], [61.13, 156.834], [56.116, 166.834]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.033, "y": 0.01}, "t": 102, "s": [{"i": [[7.108, -0.209], [0.158, 0.002], [0.045, 0.116], [0.005, 0.007], [0.024, 0.058], [0.067, 0.182], [0.004, 0.026], [0.044, 0.135], [0.025, 0.085], [0.045, 0.139], [0.004, 0.014], [0.125, 1.238], [0.002, 0.019], [-0.37, 2.25], [-0.027, 0.123], [-0.07, 0.315], [-0.027, 0.12], [-0.143, 0.522], [-0.166, 0.5], [-0.37, 0.865], [-1.074, 1.681], [-0.854, 1.046], [-0.357, 0.394], [-0.368, 0.373], [-0.187, 0.179], [-0.382, 0.337], [-0.392, 0.313], [-3.915, 0.438], [-0.167, 0.015], [-0.031, 0.001], [-0.145, 0.007], [-0.077, 0], [-0.023, 0], [-0.008, -0.003], [-0.11, -0.26], [-0.06, -0.164], [-0.005, -0.011], [-0.048, -0.135], [-0.05, -0.146], [-0.034, -0.118], [-0.026, -0.092], [-0.012, -0.052], [-0.039, -0.151], [-0.051, -0.25], [-0.024, -0.127], [-0.043, -0.311], [-0.029, -0.319], [0.001, -0.019], [-0.015, -0.217], [-0.007, -0.23], [3.673, -5.753]], "o": [[-0.161, 0.006], [-0.053, -0.115], [-0.005, -0.007], [-0.028, -0.057], [-0.074, -0.177], [-0.011, -0.021], [-0.052, -0.134], [-0.032, -0.08], [-0.045, -0.139], [-0.006, -0.011], [-0.334, -1.124], [-0.007, -0.022], [-0.204, -2.056], [0.019, -0.126], [0.051, -0.312], [0.019, -0.122], [0.115, -0.514], [0.136, -0.502], [0.287, -0.868], [0.743, -1.736], [0.759, -1.188], [0.342, -0.419], [0.357, -0.394], [0.186, -0.187], [0.374, -0.359], [0.382, -0.337], [3.762, -3.006], [0.169, -0.023], [0.034, -0.001], [0.144, -0.011], [0.076, -0.004], [0.022, -0.004], [0.007, -0.001], [0.281, 0.002], [0.067, 0.159], [0.005, 0.007], [0.051, 0.13], [0.049, 0.142], [0.037, 0.113], [0.03, 0.092], [0.016, 0.055], [0.042, 0.147], [0.057, 0.241], [0.027, 0.123], [0.057, 0.302], [0.043, 0.311], [0.003, 0.019], [0.022, 0.212], [0.013, 0.229], [0.173, 5.46], [-5.371, 8.411]], "v": [[51.843, 162.341], [51.368, 162.351], [51.217, 162.005], [51.206, 161.984], [51.129, 161.807], [50.913, 161.268], [50.886, 161.196], [50.742, 160.791], [50.657, 160.543], [50.527, 160.129], [50.516, 160.089], [49.828, 156.536], [49.822, 156.471], [50.067, 149.976], [50.132, 149.605], [50.315, 148.664], [50.389, 148.303], [50.784, 146.752], [51.24, 145.25], [52.228, 142.649], [54.951, 137.511], [57.374, 134.158], [58.42, 132.933], [59.504, 131.778], [60.059, 131.225], [61.191, 130.179], [62.353, 129.204], [74.159, 123.92], [74.663, 123.868], [74.758, 123.861], [75.192, 123.835], [75.421, 123.826], [75.494, 123.822], [75.521, 123.826], [76.169, 124.254], [76.363, 124.732], [76.376, 124.76], [76.527, 125.16], [76.676, 125.594], [76.784, 125.942], [76.867, 126.221], [76.911, 126.384], [77.032, 126.834], [77.197, 127.569], [77.271, 127.942], [77.422, 128.864], [77.53, 129.809], [77.535, 129.866], [77.586, 130.508], [77.617, 131.195], [72.39, 148.652]], "c": true}]}, {"i": {"x": 0.92, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 105, "s": [{"i": [[8.642, -1.143], [0.193, -0.017], [0.07, 0.136], [0.007, 0.008], [0.037, 0.067], [0.104, 0.213], [0.008, 0.031], [0.071, 0.16], [0.041, 0.1], [0.072, 0.164], [0.007, 0.017], [0.307, 1.494], [0.005, 0.023], [-0.169, 2.79], [-0.017, 0.154], [-0.046, 0.393], [-0.018, 0.149], [-0.109, 0.654], [-0.139, 0.63], [-0.343, 1.101], [-1.099, 2.184], [-0.91, 1.383], [-0.386, 0.526], [-0.402, 0.501], [-0.206, 0.242], [-0.423, 0.458], [-0.439, 0.431], [-4.72, 1.024], [-0.202, 0.039], [-0.037, 0.005], [-0.176, 0.027], [-0.094, 0.01], [-0.028, 0.003], [-0.01, -0.002], [-0.167, -0.303], [-0.094, -0.192], [-0.008, -0.012], [-0.076, -0.158], [-0.079, -0.172], [-0.056, -0.139], [-0.043, -0.109], [-0.021, -0.062], [-0.067, -0.18], [-0.093, -0.298], [-0.045, -0.152], [-0.092, -0.374], [-0.075, -0.385], [-0.001, -0.024], [-0.045, -0.263], [-0.038, -0.279], [3.759, -7.474]], "o": [[-0.195, 0.027], [-0.079, -0.133], [-0.007, -0.008], [-0.042, -0.066], [-0.112, -0.206], [-0.016, -0.025], [-0.08, -0.157], [-0.049, -0.093], [-0.072, -0.164], [-0.008, -0.012], [-0.548, -1.329], [-0.012, -0.026], [-0.506, -2.482], [0.007, -0.156], [0.023, -0.387], [0.008, -0.151], [0.076, -0.641], [0.104, -0.629], [0.241, -1.094], [0.688, -2.209], [0.777, -1.544], [0.364, -0.554], [0.386, -0.526], [0.203, -0.251], [0.412, -0.484], [0.423, -0.458], [4.212, -4.136], [0.204, -0.049], [0.042, -0.006], [0.175, -0.032], [0.092, -0.015], [0.027, -0.007], [0.009, -0.002], [0.343, -0.033], [0.102, 0.185], [0.007, 0.008], [0.079, 0.153], [0.078, 0.167], [0.059, 0.134], [0.048, 0.108], [0.027, 0.065], [0.07, 0.174], [0.1, 0.287], [0.048, 0.146], [0.108, 0.362], [0.092, 0.374], [0.006, 0.023], [0.053, 0.256], [0.044, 0.277], [0.894, 6.637], [-5.497, 10.929]], "v": [[56.668, 156.706], [56.089, 156.776], [55.862, 156.374], [55.846, 156.349], [55.73, 156.144], [55.399, 155.513], [55.358, 155.428], [55.131, 154.953], [54.996, 154.661], [54.786, 154.173], [54.768, 154.124], [53.484, 149.878], [53.468, 149.8], [52.955, 141.849], [52.987, 141.388], [53.093, 140.219], [53.138, 139.768], [53.426, 137.829], [53.794, 135.94], [54.673, 132.645], [57.523, 126.192], [60.059, 121.8], [61.181, 120.175], [62.358, 118.632], [62.966, 117.888], [64.215, 116.47], [65.51, 115.136], [79.506, 107.169], [80.113, 107.043], [80.228, 107.022], [80.754, 106.936], [81.033, 106.896], [81.121, 106.883], [81.155, 106.883], [81.998, 107.325], [82.295, 107.883], [82.313, 107.917], [82.548, 108.385], [82.784, 108.896], [82.959, 109.307], [83.095, 109.637], [83.169, 109.829], [83.373, 110.363], [83.667, 111.239], [83.804, 111.685], [84.102, 112.79], [84.352, 113.929], [84.366, 113.998], [84.508, 114.774], [84.632, 115.608], [80.011, 137.442]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.047, "y": 0}, "t": 108, "s": [{"i": [[10.046, -1.999], [0.225, -0.035], [0.092, 0.154], [0.009, 0.009], [0.048, 0.076], [0.139, 0.242], [0.012, 0.036], [0.095, 0.182], [0.055, 0.114], [0.097, 0.187], [0.01, 0.02], [0.474, 1.728], [0.007, 0.027], [0.014, 3.284], [-0.008, 0.182], [-0.024, 0.464], [-0.009, 0.176], [-0.077, 0.775], [-0.115, 0.75], [-0.318, 1.317], [-1.123, 2.644], [-0.962, 1.691], [-0.413, 0.646], [-0.434, 0.618], [-0.223, 0.3], [-0.461, 0.57], [-0.482, 0.539], [-5.456, 1.56], [-0.234, 0.061], [-0.043, 0.009], [-0.204, 0.045], [-0.109, 0.019], [-0.033, 0.005], [-0.012, -0.002], [-0.218, -0.342], [-0.125, -0.218], [-0.01, -0.014], [-0.101, -0.18], [-0.106, -0.196], [-0.076, -0.159], [-0.059, -0.125], [-0.029, -0.071], [-0.092, -0.206], [-0.132, -0.342], [-0.064, -0.175], [-0.136, -0.432], [-0.117, -0.446], [-0.004, -0.028], [-0.073, -0.304], [-0.065, -0.325], [3.839, -9.05]], "o": [[-0.227, 0.047], [-0.102, -0.15], [-0.009, -0.009], [-0.054, -0.074], [-0.147, -0.233], [-0.021, -0.028], [-0.106, -0.178], [-0.064, -0.105], [-0.097, -0.187], [-0.01, -0.014], [-0.744, -1.517], [-0.016, -0.029], [-0.782, -2.872], [-0.004, -0.183], [-0.002, -0.455], [-0.002, -0.178], [0.041, -0.758], [0.074, -0.746], [0.199, -1.301], [0.639, -2.643], [0.794, -1.87], [0.385, -0.677], [0.413, -0.646], [0.219, -0.31], [0.446, -0.599], [0.461, -0.57], [4.623, -5.17], [0.235, -0.073], [0.048, -0.01], [0.202, -0.05], [0.107, -0.024], [0.031, -0.01], [0.01, -0.003], [0.4, -0.064], [0.133, 0.209], [0.009, 0.009], [0.104, 0.173], [0.104, 0.19], [0.08, 0.152], [0.064, 0.123], [0.036, 0.074], [0.095, 0.199], [0.139, 0.329], [0.068, 0.168], [0.154, 0.416], [0.136, 0.432], [0.009, 0.026], [0.082, 0.296], [0.073, 0.322], [1.554, 7.714], [-5.613, 13.233]], "v": [[60.295, 150.607], [59.622, 150.734], [59.326, 150.279], [59.305, 150.251], [59.153, 150.019], [58.718, 149.305], [58.662, 149.209], [58.36, 148.669], [58.18, 148.337], [57.896, 147.78], [57.871, 147.725], [56.042, 142.844], [56.018, 142.754], [54.81, 133.47], [54.813, 132.926], [54.848, 131.547], [54.866, 131.016], [55.056, 128.72], [55.344, 126.477], [56.124, 122.546], [59.09, 114.891], [61.728, 109.548], [62.919, 107.557], [64.182, 105.657], [64.839, 104.739], [66.195, 102.981], [67.612, 101.319], [83.611, 90.895], [84.313, 90.701], [84.447, 90.668], [85.057, 90.527], [85.381, 90.459], [85.483, 90.436], [85.523, 90.435], [86.546, 90.889], [86.936, 91.52], [86.96, 91.558], [87.27, 92.089], [87.586, 92.67], [87.824, 93.138], [88.007, 93.515], [88.11, 93.735], [88.389, 94.346], [88.8, 95.35], [88.995, 95.862], [89.429, 97.135], [89.809, 98.452], [89.83, 98.532], [90.056, 99.431], [90.264, 100.4], [86.198, 126.24]], "c": true}]}, {"i": {"x": 0.88, "y": 1}, "o": {"x": 0, "y": 0}, "t": 112, "s": [{"i": [[11.823, -3.171], [0.265, -0.059], [0.122, 0.176], [0.011, 0.01], [0.064, 0.087], [0.184, 0.278], [0.017, 0.042], [0.128, 0.209], [0.075, 0.132], [0.13, 0.215], [0.013, 0.023], [0.701, 2.024], [0.011, 0.032], [0.274, 3.915], [0.004, 0.217], [0.008, 0.555], [0.003, 0.211], [-0.031, 0.93], [-0.078, 0.903], [-0.276, 1.595], [-1.132, 3.241], [-1.015, 2.092], [-0.442, 0.803], [-0.469, 0.771], [-0.242, 0.375], [-0.506, 0.716], [-0.532, 0.68], [-6.384, 2.287], [-0.274, 0.091], [-0.051, 0.014], [-0.24, 0.07], [-0.129, 0.031], [-0.039, 0.009], [-0.015, -0.001], [-0.287, -0.391], [-0.166, -0.25], [-0.014, -0.016], [-0.134, -0.207], [-0.141, -0.225], [-0.104, -0.184], [-0.08, -0.144], [-0.041, -0.082], [-0.126, -0.238], [-0.184, -0.398], [-0.09, -0.203], [-0.196, -0.504], [-0.175, -0.523], [-0.006, -0.033], [-0.111, -0.357], [-0.103, -0.382], [3.869, -11.093]], "o": [[-0.267, 0.074], [-0.134, -0.171], [-0.011, -0.01], [-0.07, -0.084], [-0.194, -0.267], [-0.027, -0.031], [-0.14, -0.204], [-0.085, -0.121], [-0.13, -0.215], [-0.014, -0.016], [-1.006, -1.75], [-0.021, -0.034], [-1.157, -3.363], [-0.019, -0.218], [-0.038, -0.542], [-0.016, -0.212], [-0.011, -0.907], [0.029, -0.895], [0.136, -1.568], [0.555, -3.202], [0.8, -2.292], [0.406, -0.838], [0.442, -0.803], [0.237, -0.387], [0.485, -0.749], [0.506, -0.716], [5.108, -6.528], [0.275, -0.105], [0.057, -0.016], [0.237, -0.076], [0.126, -0.038], [0.036, -0.015], [0.012, -0.005], [0.472, -0.108], [0.175, 0.239], [0.011, 0.01], [0.138, 0.198], [0.139, 0.219], [0.107, 0.175], [0.086, 0.141], [0.049, 0.086], [0.129, 0.23], [0.191, 0.381], [0.094, 0.195], [0.216, 0.484], [0.196, 0.504], [0.012, 0.03], [0.121, 0.346], [0.112, 0.378], [2.457, 9.077], [-5.657, 16.22]], "v": [[63.796, 144.449], [63.004, 144.653], [62.614, 144.134], [62.588, 144.103], [62.387, 143.838], [61.813, 143.021], [61.739, 142.91], [61.337, 142.29], [61.096, 141.908], [60.713, 141.266], [60.68, 141.202], [58.116, 135.525], [58.08, 135.42], [55.913, 124.443], [55.874, 123.795], [55.807, 122.148], [55.788, 121.513], [55.834, 118.76], [56.002, 116.062], [56.624, 111.314], [59.145, 101.627], [61.873, 95.049], [63.138, 92.581], [64.495, 90.217], [65.206, 89.071], [66.686, 86.869], [68.246, 84.775], [85.923, 71.238], [86.746, 70.951], [86.902, 70.901], [87.619, 70.686], [87.999, 70.579], [88.119, 70.544], [88.167, 70.539], [89.422, 71], [89.937, 71.723], [89.969, 71.766], [90.381, 72.375], [90.802, 73.043], [91.122, 73.583], [91.371, 74.018], [91.51, 74.272], [91.891, 74.979], [92.46, 76.145], [92.732, 76.74], [93.35, 78.224], [93.906, 79.764], [93.937, 79.857], [94.277, 80.912], [94.602, 82.051], [92.776, 113.364]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.037, "y": 0}, "t": 119, "s": [{"i": [[13.324, -5.664], [0.301, -0.113], [0.17, 0.19], [0.014, 0.01], [0.088, 0.093], [0.26, 0.3], [0.027, 0.047], [0.183, 0.228], [0.109, 0.144], [0.187, 0.234], [0.019, 0.025], [1.141, 2.288], [0.013, 0.038], [0.941, 4.601], [0.039, 0.257], [0.097, 0.657], [0.036, 0.25], [0.112, 1.108], [0.052, 1.084], [-0.07, 1.937], [-0.81, 4.027], [-0.855, 2.645], [-0.389, 1.022], [-0.426, 0.99], [-0.224, 0.484], [-0.478, 0.93], [-0.515, 0.893], [-7.103, 3.741], [-0.306, 0.152], [-0.057, 0.025], [-0.269, 0.121], [-0.145, 0.058], [-0.044, 0.017], [-0.017, 0.001], [-0.398, -0.418], [-0.234, -0.27], [-0.018, -0.016], [-0.19, -0.224], [-0.201, -0.244], [-0.15, -0.201], [-0.116, -0.158], [-0.061, -0.091], [-0.185, -0.262], [-0.278, -0.443], [-0.138, -0.227], [-0.309, -0.567], [-0.288, -0.592], [-0.013, -0.038], [-0.186, -0.406], [-0.182, -0.437], [2.765, -13.783]], "o": [[-0.3, 0.13], [-0.184, -0.182], [-0.014, -0.01], [-0.095, -0.089], [-0.269, -0.285], [-0.037, -0.033], [-0.196, -0.22], [-0.118, -0.13], [-0.187, -0.234], [-0.018, -0.017], [-1.454, -1.915], [-0.03, -0.037], [-1.887, -3.804], [-0.057, -0.256], [-0.131, -0.637], [-0.053, -0.249], [-0.157, -1.074], [-0.108, -1.067], [-0.09, -1.882], [0.141, -3.888], [0.572, -2.848], [0.342, -1.059], [0.389, -1.023], [0.216, -0.497], [0.448, -0.967], [0.478, -0.93], [4.939, -8.566], [0.305, -0.169], [0.064, -0.028], [0.266, -0.128], [0.141, -0.065], [0.04, -0.023], [0.013, -0.008], [0.534, -0.204], [0.243, 0.255], [0.014, 0.01], [0.192, 0.213], [0.197, 0.237], [0.153, 0.191], [0.123, 0.154], [0.071, 0.094], [0.188, 0.252], [0.284, 0.421], [0.141, 0.216], [0.329, 0.54], [0.309, 0.567], [0.019, 0.034], [0.196, 0.392], [0.191, 0.43], [4.313, 10.374], [-4.043, 20.153]], "v": [[69.371, 133.582], [68.477, 133.952], [67.94, 133.399], [67.903, 133.366], [67.627, 133.085], [66.825, 132.207], [66.721, 132.087], [66.153, 131.416], [65.811, 131.002], [65.261, 130.303], [65.212, 130.232], [61.314, 123.909], [61.255, 123.789], [56.98, 111.116], [56.832, 110.353], [56.493, 108.409], [56.369, 107.659], [55.987, 104.385], [55.756, 101.158], [55.73, 95.425], [57.143, 83.526], [59.29, 75.284], [60.378, 72.152], [61.59, 69.129], [62.24, 67.655], [63.622, 64.804], [65.114, 62.069], [83.641, 43.166], [84.557, 42.693], [84.732, 42.609], [85.536, 42.237], [85.965, 42.05], [86.099, 41.989], [86.154, 41.975], [87.695, 42.321], [88.411, 43.095], [88.456, 43.141], [89.034, 43.797], [89.633, 44.522], [90.093, 45.111], [90.453, 45.588], [90.656, 45.867], [91.213, 46.643], [92.063, 47.935], [92.476, 48.598], [93.434, 50.259], [94.328, 51.997], [94.379, 52.102], [94.944, 53.299], [95.504, 54.598], [98.334, 92.042]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 124, "s": [{"i": [[14.523, -6.995], [0.329, -0.142], [0.202, 0.196], [0.017, 0.01], [0.104, 0.096], [0.309, 0.311], [0.033, 0.05], [0.218, 0.237], [0.13, 0.151], [0.223, 0.244], [0.023, 0.026], [1.416, 2.423], [0.017, 0.04], [1.337, 4.955], [0.06, 0.278], [0.15, 0.71], [0.056, 0.27], [0.194, 1.201], [0.125, 1.178], [0.042, 2.115], [-0.653, 4.438], [-0.79, 2.935], [-0.371, 1.138], [-0.414, 1.105], [-0.22, 0.541], [-0.476, 1.043], [-0.519, 1.005], [-7.697, 4.515], [-0.332, 0.184], [-0.063, 0.03], [-0.293, 0.149], [-0.159, 0.072], [-0.048, 0.021], [-0.019, 0.002], [-0.47, -0.431], [-0.278, -0.28], [-0.021, -0.017], [-0.225, -0.232], [-0.24, -0.253], [-0.18, -0.21], [-0.14, -0.165], [-0.073, -0.095], [-0.223, -0.275], [-0.338, -0.465], [-0.168, -0.238], [-0.38, -0.599], [-0.358, -0.627], [-0.017, -0.04], [-0.233, -0.431], [-0.23, -0.465], [2.229, -15.19]], "o": [[-0.327, 0.161], [-0.216, -0.187], [-0.017, -0.01], [-0.112, -0.091], [-0.318, -0.294], [-0.043, -0.034], [-0.233, -0.227], [-0.14, -0.134], [-0.223, -0.244], [-0.022, -0.017], [-1.743, -1.997], [-0.036, -0.038], [-2.343, -4.029], [-0.079, -0.276], [-0.186, -0.686], [-0.074, -0.268], [-0.242, -1.161], [-0.187, -1.156], [-0.217, -2.045], [-0.084, -4.245], [0.462, -3.138], [0.316, -1.175], [0.371, -1.139], [0.21, -0.555], [0.44, -1.081], [0.476, -1.043], [4.98, -9.639], [0.33, -0.203], [0.07, -0.035], [0.288, -0.156], [0.154, -0.079], [0.043, -0.028], [0.014, -0.009], [0.584, -0.255], [0.287, 0.263], [0.017, 0.01], [0.228, 0.22], [0.235, 0.246], [0.183, 0.198], [0.147, 0.16], [0.085, 0.098], [0.225, 0.263], [0.343, 0.441], [0.17, 0.227], [0.401, 0.568], [0.38, 0.599], [0.024, 0.036], [0.243, 0.415], [0.24, 0.457], [5.461, 11.038], [-3.26, 22.21]], "v": [[70.891, 127.357], [69.915, 127.815], [69.281, 127.245], [69.238, 127.212], [68.913, 126.922], [67.963, 126.016], [67.839, 125.891], [67.163, 125.195], [66.755, 124.765], [66.098, 124.037], [66.039, 123.963], [61.293, 117.313], [61.22, 117.187], [55.658, 103.641], [55.445, 102.819], [54.946, 100.721], [54.761, 99.912], [54.13, 96.368], [53.671, 92.866], [53.286, 86.62], [54.123, 73.567], [56.006, 64.452], [57.026, 60.973], [58.191, 57.603], [58.825, 55.957], [60.19, 52.765], [61.685, 49.693], [81.193, 27.949], [82.187, 27.377], [82.377, 27.275], [83.252, 26.82], [83.718, 26.59], [83.865, 26.515], [83.925, 26.497], [85.667, 26.778], [86.515, 27.577], [86.567, 27.625], [87.253, 28.304], [87.967, 29.057], [88.517, 29.671], [88.949, 30.168], [89.193, 30.459], [89.864, 31.271], [90.893, 32.626], [91.395, 33.322], [92.568, 35.073], [93.675, 36.912], [93.738, 37.024], [94.443, 38.293], [95.15, 39.674], [100.64, 80.301]], "c": true}]}, {"i": {"x": 0.245, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 131, "s": [{"i": [[15.033, -8.656], [0.342, -0.178], [0.231, 0.197], [0.019, 0.009], [0.119, 0.096], [0.354, 0.313], [0.039, 0.052], [0.252, 0.239], [0.151, 0.153], [0.257, 0.247], [0.023, 0.029], [1.701, 2.513], [0.021, 0.043], [1.809, 5.256], [0.085, 0.296], [0.214, 0.756], [0.081, 0.288], [0.299, 1.283], [0.224, 1.264], [0.205, 2.283], [-0.363, 4.847], [-0.624, 3.232], [-0.311, 1.258], [-0.359, 1.226], [-0.195, 0.601], [-0.43, 1.164], [-0.48, 1.125], [-7.906, 5.461], [-0.342, 0.224], [-0.065, 0.037], [-0.303, 0.183], [-0.165, 0.09], [-0.05, 0.026], [-0.02, 0.004], [-0.537, -0.43], [-0.319, -0.282], [-0.024, -0.017], [-0.259, -0.234], [-0.276, -0.256], [-0.209, -0.214], [-0.162, -0.168], [-0.086, -0.098], [-0.26, -0.28], [-0.398, -0.477], [-0.198, -0.245], [-0.453, -0.619], [-0.431, -0.651], [-0.021, -0.042], [-0.283, -0.448], [-0.282, -0.485], [1.236, -16.59]], "o": [[-0.338, 0.198], [-0.246, -0.186], [-0.019, -0.009], [-0.127, -0.09], [-0.363, -0.294], [-0.048, -0.033], [-0.267, -0.228], [-0.16, -0.134], [-0.257, -0.247], [-0.024, -0.017], [-2.019, -2.027], [-0.041, -0.039], [-2.817, -4.179], [-0.106, -0.292], [-0.251, -0.728], [-0.1, -0.285], [-0.347, -1.237], [-0.288, -1.235], [-0.388, -2.194], [-0.412, -4.583], [0.257, -3.428], [0.25, -1.294], [0.311, -1.259], [0.183, -0.616], [0.389, -1.202], [0.43, -1.164], [4.606, -10.796], [0.338, -0.245], [0.072, -0.043], [0.297, -0.191], [0.159, -0.097], [0.044, -0.034], [0.015, -0.011], [0.606, -0.32], [0.328, 0.263], [0.019, 0.009], [0.261, 0.221], [0.27, 0.248], [0.211, 0.201], [0.17, 0.162], [0.099, 0.1], [0.261, 0.267], [0.401, 0.451], [0.2, 0.232], [0.473, 0.583], [0.453, 0.619], [0.028, 0.037], [0.292, 0.43], [0.292, 0.476], [6.69, 11.521], [-1.808, 24.256]], "v": [[73.076, 121.167], [72.065, 121.736], [71.342, 121.168], [71.293, 121.135], [70.923, 120.846], [69.836, 119.938], [69.694, 119.812], [68.917, 119.111], [68.447, 118.677], [67.687, 117.939], [67.618, 117.863], [62.028, 111.032], [61.94, 110.901], [54.951, 96.676], [54.66, 95.803], [53.966, 93.574], [53.707, 92.712], [52.761, 88.928], [52.004, 85.177], [51.117, 78.454], [51.023, 64.279], [52.35, 54.284], [53.179, 50.446], [54.172, 46.716], [54.726, 44.888], [55.946, 41.335], [57.316, 37.901], [76.572, 12.925], [77.593, 12.232], [77.789, 12.107], [78.692, 11.55], [79.174, 11.266], [79.326, 11.174], [79.389, 11.149], [81.278, 11.322], [82.247, 12.122], [82.306, 12.17], [83.093, 12.852], [83.916, 13.612], [84.552, 14.235], [85.052, 14.739], [85.335, 15.036], [86.116, 15.863], [87.322, 17.25], [87.913, 17.966], [89.302, 19.77], [90.628, 21.674], [90.705, 21.79], [91.557, 23.11], [92.419, 24.549], [101.386, 68.056]], "c": true}]}, {"t": 150, "s": [{"i": [[15.834, -9.984], [0.361, -0.207], [0.257, 0.203], [0.02, 0.009], [0.132, 0.098], [0.395, 0.322], [0.044, 0.054], [0.282, 0.248], [0.169, 0.158], [0.288, 0.255], [0.025, 0.031], [1.94, 2.639], [0.024, 0.045], [2.173, 5.595], [0.105, 0.316], [0.263, 0.807], [0.099, 0.307], [0.377, 1.372], [0.295, 1.355], [0.318, 2.455], [-0.186, 5.246], [-0.535, 3.515], [-0.282, 1.371], [-0.335, 1.339], [-0.184, 0.657], [-0.415, 1.274], [-0.47, 1.235], [-8.289, 6.231], [-0.359, 0.257], [-0.068, 0.043], [-0.319, 0.211], [-0.174, 0.104], [-0.053, 0.03], [-0.022, 0.005], [-0.597, -0.441], [-0.356, -0.29], [-0.027, -0.017], [-0.289, -0.241], [-0.308, -0.264], [-0.234, -0.221], [-0.182, -0.174], [-0.097, -0.102], [-0.292, -0.291], [-0.449, -0.498], [-0.224, -0.256], [-0.514, -0.648], [-0.492, -0.684], [-0.024, -0.045], [-0.324, -0.472], [-0.324, -0.511], [0.628, -17.955]], "o": [[-0.356, 0.228], [-0.273, -0.19], [-0.02, -0.009], [-0.14, -0.092], [-0.404, -0.302], [-0.054, -0.034], [-0.297, -0.235], [-0.178, -0.138], [-0.288, -0.255], [-0.027, -0.017], [-2.262, -2.101], [-0.046, -0.04], [-3.213, -4.389], [-0.126, -0.311], [-0.301, -0.775], [-0.12, -0.303], [-0.426, -1.32], [-0.363, -1.321], [-0.511, -2.351], [-0.639, -4.928], [0.131, -3.71], [0.214, -1.407], [0.282, -1.372], [0.172, -0.673], [0.369, -1.314], [0.415, -1.274], [4.506, -11.847], [0.354, -0.278], [0.076, -0.049], [0.312, -0.218], [0.167, -0.112], [0.046, -0.038], [0.015, -0.013], [0.64, -0.371], [0.365, 0.27], [0.02, 0.009], [0.291, 0.227], [0.302, 0.256], [0.236, 0.208], [0.19, 0.168], [0.111, 0.103], [0.293, 0.277], [0.452, 0.47], [0.225, 0.242], [0.534, 0.609], [0.514, 0.648], [0.032, 0.039], [0.333, 0.451], [0.335, 0.501], [7.698, 12.146], [-0.919, 26.253]], "v": [[72.168, 114.9], [71.103, 115.557], [70.3, 114.975], [70.246, 114.942], [69.834, 114.646], [68.625, 113.712], [68.467, 113.583], [67.599, 112.86], [67.074, 112.411], [66.224, 111.647], [66.147, 111.569], [59.832, 104.437], [59.731, 104.299], [51.596, 89.247], [51.246, 88.318], [50.403, 85.942], [50.087, 85.024], [48.908, 80.981], [47.932, 76.965], [46.69, 69.748], [45.988, 54.457], [46.994, 43.614], [47.725, 39.436], [48.637, 35.368], [49.157, 33.373], [50.321, 29.486], [51.651, 25.721], [71.344, -2.055], [72.415, -2.847], [72.621, -2.99], [73.571, -3.63], [74.079, -3.958], [74.238, -4.063], [74.305, -4.092], [76.348, -3.987], [77.426, -3.165], [77.492, -3.116], [78.369, -2.413], [79.288, -1.628], [80, -0.984], [80.56, -0.461], [80.878, -0.153], [81.755, 0.706], [83.114, 2.152], [83.78, 2.898], [85.355, 4.786], [86.864, 6.784], [86.952, 6.906], [87.926, 8.293], [88.916, 9.81], [100.426, 56.374]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.173, 0.647, 0.553, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-1827-growing-plant').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [174.09, 203.1], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 89, "op": 994, "st": 150, "bm": 0}, {"ddd": 0, "ind": 10, "ty": 4, "nm": "outline 4", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.35], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 89, "s": [-42]}, {"t": 150, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.35, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 89, "s": [217.741, 299.183, 0], "to": [-3, -53.75, 0], "ti": [-13.5, 16.75, 0]}, {"t": 150, "s": [247.741, 207.683, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [282.741, 242.683, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.35, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 89, "s": [{"i": [[-0.018, 0.02], [-0.019, -0.003], [0.018, -0.02], [0.019, 0.003]], "o": [[0.018, -0.02], [0.005, 0.018], [-0.018, 0.02], [-0.005, -0.018]], "v": [[-56.352, 62.721], [-56.293, 62.695], [-56.312, 62.757], [-56.372, 62.784]], "c": true}]}, {"t": 150, "s": [{"i": [[24.369, -27.556], [25.731, 4.072], [-24.369, 27.555], [-25.731, -4.072]], "o": [[-24.369, 27.555], [-7.188, -25.04], [24.369, -27.556], [7.188, 25.04]], "v": [[27.941, 24.71], [-54.601, 61.74], [-27.941, -24.71], [54.601, -61.74]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.071, 0.075, 0.192, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-1827-growing-plant').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 3.5, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 1), comp('wired-lineal-1827-growing-plant').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [339.685, 180.231], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 89, "op": 933, "st": 89, "bm": 0}, {"ddd": 0, "ind": 11, "ty": 4, "nm": "Leaf R 2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [305.445, 143.354, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [332.293, 166.09, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 1, "y": 1}, "o": {"x": 0.161, "y": 0}, "t": 89, "s": [{"i": [[-0.011, -0.002], [0.058, -0.479], [-0.359, -0.301], [0, 0], [0.002, -0.002], [0.005, -0.005], [0.007, -0.006], [0.004, -0.003], [0.008, -0.007], [0.001, -0.001], [0.058, -0.034], [0.006, -0.003], [0.019, -0.009], [0, 0], [-0.08, 0.659], [0, 0], [-0.01, 0.053], [-0.33, 0.218], [-0.005, 0.003], [-0.007, 0.004], [-0.006, 0.004], [-0.005, 0.003], [-0.009, 0.004]], "o": [[-0.266, 0.267], [-0.079, 0.655], [0, 0], [-0.002, 0.002], [-0.005, 0.005], [-0.007, 0.007], [-0.003, 0.003], [-0.007, 0.007], [-0.001, 0.001], [-0.054, 0.046], [-0.006, 0.004], [-0.018, 0.01], [0, 0], [-0.363, -0.299], [0, 0], [0.007, -0.055], [0.099, -0.519], [0.005, -0.003], [0.007, -0.004], [0.006, -0.004], [0.005, -0.003], [0.009, -0.005], [0.011, -0.005]], "v": [[-87.35, 153.648], [-87.871, 154.806], [-87.389, 156.359], [-87.389, 156.359], [-87.394, 156.364], [-87.41, 156.38], [-87.431, 156.4], [-87.441, 156.409], [-87.464, 156.429], [-87.467, 156.432], [-87.635, 156.551], [-87.654, 156.561], [-87.709, 156.59], [-87.709, 156.59], [-88.198, 155.031], [-88.198, 155.031], [-88.173, 154.87], [-87.479, 153.709], [-87.464, 153.699], [-87.443, 153.686], [-87.425, 153.675], [-87.41, 153.667], [-87.384, 153.653]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0, "y": 0}, "t": 95, "s": [{"i": [[-0.099, -0.022], [0.655, -4.319], [-3.168, -2.821], [0, 0], [0.016, -0.014], [0.05, -0.044], [0.067, -0.057], [0.033, -0.027], [0.072, -0.058], [0.011, -0.007], [0.535, -0.288], [0.057, -0.029], [0.171, -0.075], [0, 0], [-0.901, 5.944], [0, 0], [-0.105, 0.474], [-3.049, 1.887], [-0.045, 0.029], [-0.066, 0.037], [-0.056, 0.03], [-0.048, 0.025], [-0.079, 0.037]], "o": [[-2.481, 2.346], [-0.895, 5.907], [0, 0], [-0.016, 0.014], [-0.05, 0.046], [-0.066, 0.06], [-0.031, 0.028], [-0.07, 0.061], [-0.009, 0.008], [-0.498, 0.399], [-0.056, 0.03], [-0.168, 0.087], [0, 0], [-3.207, -2.806], [0, 0], [0.075, -0.492], [1.04, -4.671], [0.045, -0.027], [0.065, -0.038], [0.055, -0.032], [0.047, -0.026], [0.079, -0.041], [0.099, -0.046]], "v": [[-82.532, 115.272], [-87.562, 125.616], [-83.615, 139.804], [-83.614, 139.806], [-83.661, 139.85], [-83.81, 139.984], [-84.008, 140.159], [-84.105, 140.241], [-84.316, 140.416], [-84.344, 140.438], [-85.898, 141.473], [-86.067, 141.563], [-86.574, 141.807], [-86.575, 141.805], [-90.581, 127.564], [-90.582, 127.563], [-90.31, 126.114], [-83.713, 115.79], [-83.578, 115.707], [-83.383, 115.595], [-83.215, 115.502], [-83.074, 115.426], [-82.837, 115.308]], "c": true}]}, {"i": {"x": 0.976, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 99, "s": [{"i": [[-0.172, -0.051], [1.73, -7.527], [-5.208, -5.395], [0, 0], [0.03, -0.023], [0.093, -0.07], [0.125, -0.091], [0.062, -0.043], [0.134, -0.092], [0.02, -0.011], [0.981, -0.437], [0.104, -0.044], [0.311, -0.11], [0, 0], [-2.381, 10.357], [0, 0], [-0.249, 0.821], [-5.627, 2.92], [-0.084, 0.045], [-0.121, 0.056], [-0.103, 0.046], [-0.088, 0.038], [-0.144, 0.055]], "o": [[-4.687, 3.805], [-2.366, 10.293], [0, 0], [-0.03, 0.023], [-0.094, 0.074], [-0.125, 0.097], [-0.058, 0.045], [-0.131, 0.097], [-0.016, 0.012], [-0.932, 0.637], [-0.103, 0.046], [-0.307, 0.13], [0, 0], [-5.278, -5.373], [0, 0], [0.198, -0.858], [2.456, -8.095], [0.083, -0.042], [0.119, -0.058], [0.102, -0.049], [0.086, -0.04], [0.144, -0.062], [0.18, -0.068]], "v": [[-75.197, 81.247], [-85.442, 98.811], [-80.378, 124.348], [-80.376, 124.351], [-80.465, 124.422], [-80.745, 124.64], [-81.118, 124.921], [-81.299, 125.053], [-81.695, 125.333], [-81.748, 125.369], [-84.624, 126.986], [-84.935, 127.122], [-85.861, 127.483], [-85.863, 127.481], [-91.025, 101.842], [-91.027, 101.84], [-90.354, 99.323], [-77.348, 82.003], [-77.099, 81.875], [-76.739, 81.702], [-76.431, 81.562], [-76.172, 81.447], [-75.739, 81.271]], "c": true}]}, {"i": {"x": 0.973, "y": 1}, "o": {"x": 0.04, "y": 0}, "t": 102, "s": [{"i": [[-0.225, -0.073], [2.506, -9.839], [-6.679, -7.251], [0, 0], [0.04, -0.029], [0.125, -0.09], [0.167, -0.115], [0.083, -0.055], [0.179, -0.117], [0.027, -0.014], [1.303, -0.544], [0.139, -0.054], [0.413, -0.135], [0, 0], [-3.448, 13.539], [0, 0], [-0.353, 1.071], [-7.485, 3.664], [-0.111, 0.057], [-0.16, 0.07], [-0.137, 0.057], [-0.117, 0.047], [-0.191, 0.068]], "o": [[-6.277, 4.856], [-3.427, 13.456], [0, 0], [-0.04, 0.029], [-0.126, 0.095], [-0.167, 0.124], [-0.077, 0.057], [-0.175, 0.124], [-0.022, 0.016], [-1.244, 0.809], [-0.137, 0.057], [-0.407, 0.162], [0, 0], [-6.772, -7.224], [0, 0], [0.286, -1.121], [3.477, -10.563], [0.11, -0.052], [0.159, -0.073], [0.135, -0.061], [0.115, -0.05], [0.191, -0.077], [0.238, -0.084]], "v": [[-66.897, 56.934], [-80.903, 79.703], [-75.032, 113.422], [-75.03, 113.425], [-75.15, 113.516], [-75.524, 113.793], [-76.022, 114.152], [-76.265, 114.319], [-76.794, 114.676], [-76.865, 114.72], [-80.694, 116.757], [-81.107, 116.926], [-82.336, 117.373], [-82.338, 117.37], [-88.334, 83.515], [-88.336, 83.512], [-87.374, 80.224], [-69.748, 57.862], [-69.416, 57.7], [-68.938, 57.485], [-68.53, 57.309], [-68.185, 57.167], [-67.61, 56.949]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0, "y": 0}, "t": 108, "s": [{"i": [[-0.314, -0.143], [5.418, -13.646], [-8.411, -11.543], [0, 0], [0.064, -0.035], [0.197, -0.106], [0.263, -0.136], [0.13, -0.065], [0.281, -0.137], [0.04, -0.019], [1.994, -0.552], [0.211, -0.054], [0.625, -0.121], [0, 0], [-7.455, 18.778], [0, 0], [-0.706, 1.472], [-11.55, 3.94], [-0.172, 0.062], [-0.246, 0.071], [-0.209, 0.058], [-0.178, 0.047], [-0.29, 0.064]], "o": [[-10.007, 5.856], [-7.41, 18.662], [0, 0], [-0.064, 0.035], [-0.201, 0.114], [-0.265, 0.148], [-0.123, 0.068], [-0.277, 0.147], [-0.035, 0.018], [-1.956, 0.941], [-0.21, 0.058], [-0.622, 0.161], [0, 0], [-8.551, -11.522], [0, 0], [0.619, -1.555], [6.962, -14.514], [0.169, -0.055], [0.244, -0.077], [0.208, -0.063], [0.176, -0.052], [0.292, -0.076], [0.362, -0.078]], "v": [[-44.439, 17.451], [-68.918, 47.601], [-66.451, 96.888], [-66.449, 96.893], [-66.639, 97.003], [-67.233, 97.335], [-68.023, 97.761], [-68.406, 97.957], [-69.24, 98.376], [-69.351, 98.428], [-75.29, 100.676], [-75.92, 100.845], [-77.789, 101.271], [-77.791, 101.266], [-80.416, 51.761], [-80.418, 51.756], [-78.426, 47.218], [-48.753, 18.283], [-48.242, 18.11], [-47.508, 17.885], [-46.882, 17.705], [-46.354, 17.561], [-45.479, 17.349]], "c": true}]}, {"i": {"x": 0.811, "y": 0.837}, "o": {"x": 0.167, "y": 0.167}, "t": 112, "s": [{"i": [[-0.351, -0.193], [7.434, -15.472], [-8.733, -14.168], [0, 0], [0.077, -0.035], [0.237, -0.108], [0.316, -0.137], [0.156, -0.065], [0.337, -0.136], [0.048, -0.019], [2.352, -0.479], [0.249, -0.045], [0.733, -0.089], [0, 0], [-10.23, 21.291], [0, 0], [-0.943, 1.659], [-13.691, 3.638], [-0.205, 0.058], [-0.291, 0.063], [-0.247, 0.051], [-0.21, 0.04], [-0.341, 0.051]], "o": [[-12.071, 6.002], [-10.167, 21.16], [0, 0], [-0.077, 0.035], [-0.242, 0.116], [-0.319, 0.151], [-0.148, 0.069], [-0.333, 0.149], [-0.042, 0.019], [-2.341, 0.936], [-0.247, 0.051], [-0.733, 0.136], [0, 0], [-8.897, -14.154], [0, 0], [0.849, -1.763], [9.294, -16.357], [0.2, -0.051], [0.289, -0.069], [0.245, -0.057], [0.208, -0.046], [0.344, -0.065], [0.425, -0.061]], "v": [[-27.983, -2.887], [-58.868, 30.259], [-60.244, 87.974], [-60.242, 87.98], [-60.472, 88.092], [-61.187, 88.43], [-62.136, 88.861], [-62.596, 89.059], [-63.596, 89.478], [-63.729, 89.529], [-70.787, 91.659], [-71.53, 91.805], [-73.727, 92.146], [-73.729, 92.139], [-72.517, 34.159], [-72.518, 34.152], [-69.827, 29.023], [-33.041, -2.275], [-32.436, -2.435], [-31.568, -2.636], [-30.828, -2.794], [-30.206, -2.918], [-29.176, -3.093]], "c": true}]}, {"i": {"x": 0.667, "y": 0.857}, "o": {"x": 0.007, "y": 0.006}, "t": 119, "s": [{"i": [[-0.407, -0.274], [11.025, -17.663], [-8.968, -18.049], [0, 0], [0.099, -0.033], [0.304, -0.101], [0.405, -0.126], [0.199, -0.059], [0.43, -0.123], [0.062, -0.011], [2.944, -0.29], [0.311, -0.025], [0.91, -0.019], [0, 0], [-15.172, 24.306], [0, 0], [-1.361, 1.876], [-17.243, 2.712], [-0.258, 0.045], [-0.364, 0.04], [-0.309, 0.031], [-0.262, 0.023], [-0.425, 0.02]], "o": [[-15.547, 5.743], [-15.078, 24.156], [0, 0], [-0.099, 0.033], [-0.311, 0.11], [-0.41, 0.143], [-0.19, 0.065], [-0.427, 0.138], [-0.053, 0.017], [-2.988, 0.84], [-0.31, 0.031], [-0.915, 0.075], [0, 0], [-9.17, -18.052], [0, 0], [1.258, -2.012], [13.416, -18.5], [0.252, -0.036], [0.363, -0.048], [0.308, -0.038], [0.261, -0.03], [0.43, -0.036], [0.529, -0.022]], "v": [[0.807, -27.487], [-41.163, 8.548], [-49.961, 77.614], [-49.959, 77.622], [-50.254, 77.729], [-51.173, 78.047], [-52.391, 78.45], [-52.98, 78.632], [-54.258, 79.014], [-54.427, 79.06], [-63.348, 80.763], [-64.278, 80.848], [-67.015, 80.992], [-67.016, 80.984], [-58.387, 11.578], [-58.389, 11.571], [-54.455, 5.742], [-5.474, -27.364], [-4.712, -27.482], [-3.622, -27.619], [-2.695, -27.719], [-1.917, -27.793], [-0.631, -27.878]], "c": true}]}, {"i": {"x": 0.364, "y": 1}, "o": {"x": 0.051, "y": 0.022}, "t": 131, "s": [{"i": [[-0.433, -0.375], [15.196, -19.251], [-7.91, -22.341], [0, 0], [0.119, -0.026], [0.367, -0.078], [0.486, -0.095], [0.239, -0.042], [0.515, -0.087], [0.074, -0.005], [3.45, 0.048], [0.363, 0.011], [1.056, 0.098], [0, 0], [-20.911, 26.492], [0, 0], [-1.833, 2.02], [-20.346, 0.904], [-0.305, 0.019], [-0.427, -0.001], [-0.363, -0.005], [-0.307, -0.008], [-0.495, -0.033]], "o": [[-18.798, 4.681], [-20.782, 26.328], [0, 0], [-0.119, 0.026], [-0.375, 0.088], [-0.494, 0.113], [-0.229, 0.051], [-0.514, 0.105], [-0.064, 0.013], [-3.576, 0.59], [-0.363, -0.005], [-1.071, -0.033], [0, 0], [-8.145, -22.372], [0, 0], [1.733, -2.193], [18.08, -19.917], [0.297, -0.01], [0.427, -0.009], [0.362, -0.004], [0.307, -0.001], [0.503, 0.014], [0.616, 0.044]], "v": [[35.329, -49.325], [-18.235, -12.62], [-37.906, 67.184], [-37.905, 67.193], [-38.262, 67.279], [-39.37, 67.531], [-40.836, 67.843], [-41.543, 67.979], [-43.076, 68.258], [-43.278, 68.289], [-53.845, 69.108], [-54.934, 69.085], [-58.125, 68.893], [-58.125, 68.884], [-38.603, -11.341], [-38.603, -11.35], [-33.246, -17.663], [28.036, -50.009], [28.935, -50.047], [30.216, -50.064], [31.304, -50.059], [32.215, -50.043], [33.716, -49.973]], "c": true}]}, {"t": 150, "s": [{"i": [[-0.44, -0.44], [17.71, -20.02], [-6.94, -24.99], [0, 0], [0.13, -0.02], [0.4, -0.06], [0.53, -0.07], [0.26, -0.03], [0.56, -0.06], [0.08, 0], [3.71, 0.29], [0.39, 0.04], [1.13, 0.18], [0, 0], [-24.37, 27.55], [0, 0], [-2.12, 2.08], [-21.96, -0.41], [-0.33, 0], [-0.46, -0.03], [-0.39, -0.03], [-0.33, -0.03], [-0.53, -0.07]], "o": [[-20.56, 3.83], [-24.22, 27.38], [0, 0], [-0.13, 0.02], [-0.41, 0.07], [-0.54, 0.09], [-0.25, 0.04], [-0.56, 0.08], [-0.07, 0.01], [-3.89, 0.4], [-0.39, -0.03], [-1.15, -0.11], [0, 0], [-7.19, -25.04], [0, 0], [2.02, -2.28], [20.86, -20.55], [0.32, 0.01], [0.46, 0.02], [0.39, 0.02], [0.33, 0.02], [0.54, 0.05], [0.66, 0.09]], "v": [[54.622, -59.69], [-5.608, -23.21], [-32.398, 62.76], [-32.398, 62.77], [-32.788, 62.84], [-33.998, 63.04], [-35.598, 63.28], [-36.368, 63.38], [-38.038, 63.58], [-38.258, 63.6], [-49.688, 63.77], [-50.858, 63.67], [-54.278, 63.24], [-54.278, 63.23], [-27.618, -23.21], [-27.618, -23.22], [-21.408, -29.76], [46.822, -60.94], [47.792, -60.92], [49.172, -60.85], [50.342, -60.77], [51.322, -60.69], [52.932, -60.51]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.173, 0.647, 0.553, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-1827-growing-plant').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [330.969, 166.09], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 50, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 1, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 1, "y": 1}, "o": {"x": 0.161, "y": 0}, "t": 89, "s": [{"i": [[0.078, -0.642], [0.269, -0.267], [0, 0], [0.002, -0.002], [0.005, -0.005], [0.007, -0.006], [0.004, -0.003], [0.008, -0.007], [0.001, -0.001], [0.058, -0.034], [0.006, -0.003], [0.019, -0.009], [-0.08, 0.659], [0, 0], [-0.01, 0.053], [-0.33, 0.218], [-0.005, 0.003], [-0.007, 0.004], [-0.006, 0.004], [-0.005, 0.003], [-0.009, 0.004], [-0.011, -0.002], [-0.008, -0.007]], "o": [[-0.059, 0.482], [0, 0], [-0.002, 0.002], [-0.005, 0.005], [-0.007, 0.007], [-0.003, 0.003], [-0.007, 0.007], [-0.001, 0.001], [-0.054, 0.046], [-0.006, 0.004], [-0.018, 0.01], [-0.363, -0.299], [0, 0], [0.007, -0.054], [0.099, -0.519], [0.005, -0.003], [0.007, -0.004], [0.006, -0.004], [0.005, -0.003], [0.009, -0.005], [0.011, -0.005], [0.01, 0.002], [0.345, 0.307]], "v": [[-88.186, 155.199], [-88.713, 156.363], [-88.713, 156.363], [-88.718, 156.368], [-88.734, 156.384], [-88.755, 156.404], [-88.765, 156.413], [-88.788, 156.433], [-88.791, 156.436], [-88.959, 156.555], [-88.978, 156.565], [-89.033, 156.594], [-89.522, 155.035], [-89.522, 155.035], [-89.497, 154.874], [-88.803, 153.713], [-88.788, 153.703], [-88.767, 153.69], [-88.749, 153.679], [-88.733, 153.67], [-88.708, 153.657], [-88.674, 153.652], [-88.647, 153.665]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0, "y": 0}, "t": 95, "s": [{"i": [[0.878, -5.785], [2.508, -2.344], [0, 0], [0.016, -0.014], [0.05, -0.044], [0.067, -0.057], [0.033, -0.027], [0.072, -0.058], [0.011, -0.007], [0.534, -0.29], [0.057, -0.029], [0.171, -0.075], [-0.901, 5.944], [0, 0], [-0.106, 0.475], [-3.05, 1.886], [-0.045, 0.029], [-0.066, 0.037], [-0.056, 0.03], [-0.048, 0.025], [-0.079, 0.037], [-0.099, -0.022], [-0.07, -0.066]], "o": [[-0.66, 4.35], [0, 0], [-0.016, 0.014], [-0.05, 0.046], [-0.066, 0.06], [-0.031, 0.028], [-0.07, 0.061], [-0.009, 0.008], [-0.501, 0.399], [-0.056, 0.03], [-0.17, 0.086], [-3.207, -2.806], [0, 0], [0.074, -0.491], [1.039, -4.67], [0.045, -0.027], [0.065, -0.038], [0.055, -0.032], [0.047, -0.026], [0.079, -0.041], [0.099, -0.046], [0.091, 0.017], [3.043, 2.868]], "v": [[-79.856, 129.413], [-84.939, 139.808], [-84.938, 139.809], [-84.985, 139.853], [-85.133, 139.988], [-85.332, 140.163], [-85.428, 140.245], [-85.64, 140.42], [-85.668, 140.442], [-87.221, 141.477], [-87.391, 141.567], [-87.899, 141.809], [-91.905, 127.568], [-91.906, 127.567], [-91.634, 126.118], [-85.037, 115.794], [-84.902, 115.711], [-84.707, 115.598], [-84.539, 115.506], [-84.398, 115.43], [-84.161, 115.312], [-83.856, 115.276], [-83.611, 115.402]], "c": true}]}, {"i": {"x": 0.976, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 99, "s": [{"i": [[2.32, -10.082], [4.734, -3.797], [0, 0], [0.03, -0.023], [0.093, -0.07], [0.125, -0.091], [0.062, -0.043], [0.134, -0.092], [0.02, -0.011], [0.98, -0.44], [0.104, -0.044], [0.311, -0.11], [-2.381, 10.357], [0, 0], [-0.252, 0.822], [-5.628, 2.917], [-0.084, 0.045], [-0.121, 0.056], [-0.103, 0.046], [-0.088, 0.038], [-0.144, 0.055], [-0.172, -0.051], [-0.114, -0.125]], "o": [[-1.744, 7.58], [0, 0], [-0.03, 0.023], [-0.094, 0.074], [-0.125, 0.097], [-0.058, 0.045], [-0.131, 0.097], [-0.016, 0.012], [-0.936, 0.636], [-0.103, 0.046], [-0.311, 0.129], [-5.278, -5.373], [0, 0], [0.195, -0.856], [2.454, -8.094], [0.083, -0.042], [0.119, -0.058], [0.102, -0.049], [0.086, -0.04], [0.144, -0.062], [0.18, -0.068], [0.158, 0.043], [4.982, 5.462]], "v": [[-71.355, 106.706], [-81.701, 124.352], [-81.7, 124.355], [-81.789, 124.426], [-82.069, 124.644], [-82.441, 124.925], [-82.623, 125.056], [-83.019, 125.337], [-83.072, 125.373], [-85.948, 126.99], [-86.258, 127.126], [-87.187, 127.485], [-92.349, 101.846], [-92.351, 101.844], [-91.678, 99.327], [-78.672, 82.007], [-78.423, 81.878], [-78.063, 81.706], [-77.755, 81.565], [-77.496, 81.451], [-77.062, 81.275], [-76.521, 81.25], [-76.106, 81.506]], "c": true}]}, {"i": {"x": 0.973, "y": 1}, "o": {"x": 0.04, "y": 0}, "t": 102, "s": [{"i": [[3.359, -13.179], [6.338, -4.845], [0, 0], [0.04, -0.029], [0.125, -0.09], [0.167, -0.115], [0.083, -0.055], [0.179, -0.117], [0.027, -0.014], [1.301, -0.548], [0.139, -0.054], [0.413, -0.135], [-3.448, 13.539], [0, 0], [-0.358, 1.072], [-7.487, 3.661], [-0.111, 0.057], [-0.16, 0.07], [-0.137, 0.057], [-0.117, 0.047], [-0.191, 0.068], [-0.225, -0.073], [-0.146, -0.168]], "o": [[-2.526, 9.909], [0, 0], [-0.04, 0.029], [-0.126, 0.095], [-0.167, 0.124], [-0.077, 0.057], [-0.175, 0.124], [-0.022, 0.016], [-1.249, 0.807], [-0.137, 0.057], [-0.412, 0.16], [-6.772, -7.224], [0, 0], [0.283, -1.119], [3.474, -10.562], [0.11, -0.052], [0.159, -0.073], [0.135, -0.061], [0.115, -0.05], [0.191, -0.077], [0.238, -0.084], [0.206, 0.061], [6.379, 7.332]], "v": [[-62.215, 90.552], [-76.356, 113.425], [-76.354, 113.429], [-76.474, 113.52], [-76.848, 113.797], [-77.346, 114.155], [-77.589, 114.323], [-78.118, 114.68], [-78.189, 114.724], [-82.018, 116.761], [-82.431, 116.93], [-83.662, 117.374], [-89.658, 83.519], [-89.66, 83.516], [-88.698, 80.228], [-71.071, 57.865], [-70.74, 57.704], [-70.262, 57.489], [-69.853, 57.313], [-69.509, 57.171], [-68.934, 56.953], [-68.221, 56.937], [-67.684, 57.286]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0, "y": 0}, "t": 108, "s": [{"i": [[7.261, -18.278], [10.094, -5.829], [0, 0], [0.064, -0.035], [0.197, -0.106], [0.263, -0.136], [0.13, -0.065], [0.281, -0.137], [0.04, -0.019], [1.992, -0.557], [0.211, -0.053], [0.625, -0.121], [-7.455, 18.778], [0, 0], [-0.708, 1.476], [-11.552, 3.935], [-0.172, 0.062], [-0.246, 0.071], [-0.209, 0.058], [-0.178, 0.047], [-0.29, 0.064], [-0.314, -0.143], [-0.183, -0.266]], "o": [[-5.459, 13.742], [0, 0], [-0.064, 0.035], [-0.201, 0.114], [-0.265, 0.148], [-0.123, 0.068], [-0.277, 0.147], [-0.035, 0.018], [-1.963, 0.938], [-0.21, 0.058], [-0.629, 0.158], [-8.551, -11.522], [0, 0], [0.613, -1.553], [6.957, -14.512], [0.169, -0.055], [0.244, -0.077], [0.208, -0.063], [0.176, -0.052], [0.292, -0.076], [0.362, -0.078], [0.289, 0.123], [7.96, 11.607]], "v": [[-43.081, 66.618], [-67.775, 96.892], [-67.773, 96.897], [-67.963, 97.007], [-68.557, 97.339], [-69.347, 97.764], [-69.73, 97.961], [-70.564, 98.38], [-70.675, 98.432], [-76.613, 100.68], [-77.244, 100.849], [-79.115, 101.269], [-81.74, 51.765], [-81.742, 51.76], [-79.75, 47.222], [-50.077, 18.287], [-49.566, 18.113], [-48.832, 17.889], [-48.205, 17.709], [-47.678, 17.565], [-46.803, 17.353], [-45.763, 17.455], [-45.044, 18.048]], "c": true}]}, {"i": {"x": 0.811, "y": 0.837}, "o": {"x": 0.167, "y": 0.167}, "t": 112, "s": [{"i": [[9.962, -20.724], [12.169, -5.964], [0, 0], [0.077, -0.035], [0.237, -0.108], [0.316, -0.137], [0.156, -0.065], [0.337, -0.136], [0.048, -0.019], [2.35, -0.485], [0.249, -0.044], [0.733, -0.089], [-10.23, 21.291], [0, 0], [-0.945, 1.664], [-13.692, 3.632], [-0.205, 0.058], [-0.291, 0.063], [-0.247, 0.051], [-0.21, 0.04], [-0.341, 0.051], [-0.351, -0.193], [-0.188, -0.326]], "o": [[-7.49, 15.581], [0, 0], [-0.077, 0.035], [-0.242, 0.116], [-0.319, 0.151], [-0.148, 0.069], [-0.333, 0.149], [-0.042, 0.019], [-2.349, 0.931], [-0.247, 0.051], [-0.741, 0.132], [-8.897, -14.154], [0, 0], [0.842, -1.761], [9.288, -16.355], [0.2, -0.051], [0.289, -0.069], [0.245, -0.057], [0.208, -0.046], [0.344, -0.065], [0.425, -0.061], [0.324, 0.168], [8.206, 14.205]], "v": [[-30.424, 54.704], [-61.568, 87.978], [-61.566, 87.984], [-61.796, 88.096], [-62.51, 88.434], [-63.46, 88.865], [-63.92, 89.063], [-64.92, 89.482], [-65.053, 89.533], [-72.111, 91.663], [-72.854, 91.809], [-75.053, 92.143], [-73.84, 34.162], [-73.842, 34.156], [-71.15, 29.027], [-34.365, -2.271], [-33.76, -2.431], [-32.892, -2.632], [-32.152, -2.79], [-31.53, -2.915], [-30.5, -3.089], [-29.307, -2.883], [-28.527, -2.132]], "c": true}]}, {"i": {"x": 0.667, "y": 0.857}, "o": {"x": 0.007, "y": 0.006}, "t": 119, "s": [{"i": [[14.774, -23.658], [15.663, -5.685], [0, 0], [0.099, -0.033], [0.304, -0.101], [0.405, -0.126], [0.199, -0.059], [0.43, -0.123], [0.062, -0.011], [2.943, -0.298], [0.311, -0.023], [0.91, -0.019], [-15.172, 24.306], [0, 0], [-1.364, 1.881], [-17.244, 2.704], [-0.258, 0.045], [-0.364, 0.04], [-0.309, 0.031], [-0.262, 0.023], [-0.425, 0.02], [-0.407, -0.274], [-0.191, -0.414]], "o": [[-11.108, 17.787], [0, 0], [-0.099, 0.033], [-0.311, 0.11], [-0.41, 0.143], [-0.19, 0.065], [-0.427, 0.138], [-0.053, 0.017], [-2.997, 0.834], [-0.31, 0.031], [-0.925, 0.068], [-9.17, -18.052], [0, 0], [1.25, -2.011], [13.408, -18.498], [0.252, -0.036], [0.363, -0.048], [0.308, -0.038], [0.261, -0.03], [0.43, -0.036], [0.529, -0.022], [0.376, 0.24], [8.318, 18.03]], "v": [[-8.981, 41.462], [-51.284, 77.618], [-51.283, 77.626], [-51.578, 77.733], [-52.497, 78.051], [-53.715, 78.454], [-54.303, 78.636], [-55.582, 79.018], [-55.751, 79.064], [-64.672, 80.767], [-65.602, 80.852], [-68.34, 80.988], [-59.711, 11.582], [-59.713, 11.574], [-55.779, 5.746], [-6.797, -27.36], [-6.035, -27.478], [-4.946, -27.615], [-4.019, -27.715], [-3.241, -27.789], [-1.955, -27.874], [-0.517, -27.483], [0.347, -26.489]], "c": true}]}, {"i": {"x": 0.364, "y": 1}, "o": {"x": 0.051, "y": 0.022}, "t": 131, "s": [{"i": [[20.361, -25.784], [18.924, -4.598], [0, 0], [0.119, -0.026], [0.367, -0.078], [0.486, -0.095], [0.239, -0.042], [0.515, -0.087], [0.074, -0.005], [3.45, 0.039], [0.363, 0.014], [1.056, 0.098], [-20.911, 26.492], [0, 0], [-1.847, 2.017], [-20.346, 0.895], [-0.305, 0.019], [-0.427, -0.001], [-0.363, -0.005], [-0.307, -0.008], [-0.495, -0.033], [-0.433, -0.375], [-0.164, -0.51]], "o": [[-15.308, 19.386], [0, 0], [-0.119, 0.026], [-0.375, 0.088], [-0.494, 0.113], [-0.229, 0.051], [-0.514, 0.105], [-0.064, 0.013], [-3.586, 0.582], [-0.363, -0.005], [-1.081, -0.042], [-8.145, -22.372], [0, 0], [1.724, -2.192], [18.071, -19.916], [0.297, -0.01], [0.427, -0.009], [0.362, -0.004], [0.307, -0.001], [0.503, 0.014], [0.616, 0.044], [0.403, 0.331], [7.16, 22.233]], "v": [[14.736, 30.385], [-39.23, 67.187], [-39.229, 67.197], [-39.586, 67.283], [-40.693, 67.535], [-42.16, 67.847], [-42.866, 67.983], [-44.4, 68.262], [-44.602, 68.293], [-55.169, 69.112], [-56.258, 69.089], [-59.449, 68.887], [-39.926, -11.337], [-39.927, -11.346], [-34.57, -17.66], [26.712, -50.005], [27.611, -50.043], [28.892, -50.06], [29.98, -50.055], [30.891, -50.039], [32.392, -49.969], [34.005, -49.321], [34.869, -48.041]], "c": true}]}, {"t": 150, "s": [{"i": [[23.72, -26.82], [20.69, -3.73], [0, 0], [0.13, -0.02], [0.4, -0.06], [0.53, -0.07], [0.26, -0.03], [0.56, -0.06], [0.08, 0], [3.71, 0.28], [0.39, 0.04], [1.13, 0.18], [-24.37, 27.55], [0, 0], [-2.13, 2.08], [-21.96, -0.42], [-0.33, 0], [-0.46, -0.03], [-0.39, -0.03], [-0.33, -0.03], [-0.53, -0.07], [-0.44, -0.44], [-0.14, -0.57]], "o": [[-17.84, 20.16], [0, 0], [-0.13, 0.02], [-0.41, 0.07], [-0.54, 0.09], [-0.25, 0.04], [-0.56, 0.08], [-0.07, 0.01], [-3.9, 0.39], [-0.39, -0.03], [-1.16, -0.12], [-7.19, -25.04], [0, 0], [2.01, -2.28], [20.85, -20.55], [0.32, 0.01], [0.46, 0.02], [0.39, 0.02], [0.33, 0.02], [0.54, 0.05], [0.66, 0.09], [0.41, 0.39], [6.14, 24.82]], "v": [[26.948, 26.204], [-33.722, 62.764], [-33.722, 62.774], [-34.112, 62.844], [-35.322, 63.044], [-36.922, 63.284], [-37.692, 63.384], [-39.362, 63.584], [-39.582, 63.604], [-51.012, 63.774], [-52.182, 63.674], [-55.602, 63.234], [-28.942, -23.206], [-28.942, -23.216], [-22.732, -29.756], [45.498, -60.936], [46.468, -60.916], [47.848, -60.846], [49.018, -60.766], [49.998, -60.686], [51.608, -60.506], [53.298, -59.686], [54.138, -58.226]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.173, 0.647, 0.553, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-1827-growing-plant').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [332.293, 166.086], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 89, "op": 994, "st": 150, "bm": 0}, {"ddd": 0, "ind": 12, "ty": 4, "nm": "Shape Layer 1", "parent": 16, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [250.5, 322.424, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [39, 39], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "Ellipse Path 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 32, "s": [48]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 35, "s": [30.545]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 40, "s": [11.455]}, {"t": 42, "s": [0]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 32, "s": [50]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 35, "s": [68.182]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 40, "s": [88.068]}, {"t": 42, "s": [100]}], "ix": 2}, "o": {"a": 0, "k": 180, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.071, 0.075, 0.192, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-1827-growing-plant').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 3.5, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 1), comp('wired-lineal-1827-growing-plant').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0.5, 33.5], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Ellipse 1", "np": 4, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.573, 0.078, 0.047, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-1827-growing-plant').layer('control').effect('quaternary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".quaternary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "quaternary"}], "ip": 32, "op": 103, "st": 0, "bm": 0}, {"ddd": 0, "ind": 13, "ty": 4, "nm": "outline 6", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.68], "y": [0]}, "t": 81, "s": [0]}, {"t": 102, "s": [69]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.425, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 31, "s": [214.215, 300, 0], "to": [0, -11.667, 0], "ti": [0, 7, 0]}, {"i": {"x": 0.557, "y": 1}, "o": {"x": 0.49, "y": 0}, "t": 58, "s": [214.215, 230, 0], "to": [0, 8.5, 0], "ti": [-0.333, -12.5, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.68, "y": 0}, "t": 81, "s": [214.215, 258, 0], "to": [8.333, -214, 0], "ti": [-67.333, 34.667, 0]}, {"t": 102, "s": [404.215, -78, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [249.215, 335, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 18, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 35, "s": [100, 116, 100]}, {"t": 45.009765625, "s": [100, 100, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[0, 0], [-8.836, -48.127], [0.33, -7.91], [-9.97, 23.673], [-16.052, 19.969], [-19.584, 21.723]], "o": [[16.896, 9.023], [1.673, 9.113], [-0.984, -10.773], [4.167, -9.894], [11.157, -13.879], [0, 0]], "v": [[-71.429, -39.055], [-10.511, 48.015], [-8.742, 73.66], [-0.206, 18.027], [25.169, -19.368], [71.429, -73.66]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 17, "s": [{"i": [[0, 0], [-8.836, -48.127], [0.33, -7.91], [-2.886, 25.524], [-3.023, 25.23], [-2.205, 29.164]], "o": [[4.005, 20.498], [1.673, 9.113], [-0.984, -10.773], [1.665, -14.729], [2.119, -17.681], [0, 0]], "v": [[-29.921, -49.838], [-10.511, 48.015], [-8.742, 73.66], [-7.081, 17.389], [-3.392, -25.57], [-0.005, -96.816]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 40, "s": [{"i": [[0, 0], [-8.836, -48.127], [0.33, -7.91], [-4.364, 25.138], [-5.742, 24.132], [-5.831, 27.612]], "o": [[6.695, 18.104], [1.673, 9.113], [-0.984, -10.773], [2.187, -13.721], [4.004, -16.888], [0, 0]], "v": [[-38.581, -47.588], [-10.511, 48.015], [-8.742, 73.66], [-5.646, 17.522], [2.567, -24.276], [14.898, -91.985]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 59, "s": [{"i": [[0, 0], [-8.836, -48.127], [0.33, -7.91], [-2.886, 25.524], [-3.023, 25.23], [-2.205, 29.164]], "o": [[4.005, 20.498], [1.673, 9.113], [-0.984, -10.773], [1.665, -14.729], [2.119, -17.681], [0, 0]], "v": [[-30.225, -29.838], [-10.511, 48.015], [-8.742, 73.66], [-7.081, 17.389], [-3.696, -5.57], [-0.309, -76.816]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 77, "s": [{"i": [[0, 0], [-8.836, -48.127], [0.33, -7.91], [-4.364, 25.138], [-5.742, 24.132], [-5.831, 27.612]], "o": [[6.695, 18.104], [1.673, 9.113], [-0.984, -10.773], [2.187, -13.721], [4.004, -16.888], [0, 0]], "v": [[-38.581, -47.588], [-10.511, 48.015], [-8.742, 73.66], [-5.646, 17.522], [2.567, -24.276], [14.898, -91.985]], "c": false}]}, {"t": 102, "s": [{"i": [[0, 0], [-8.836, -48.127], [0.33, -7.91], [-9.97, 23.673], [-16.052, 19.969], [-19.584, 21.723]], "o": [[16.896, 9.023], [1.673, 9.113], [-0.984, -10.773], [4.167, -9.894], [11.157, -13.879], [0, 0]], "v": [[-71.429, -39.055], [-10.511, 48.015], [-8.742, 73.66], [-0.206, 18.027], [25.169, -19.368], [71.429, -73.66]], "c": false}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.071, 0.075, 0.192, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-1827-growing-plant').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 3.5, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 1), comp('wired-lineal-1827-growing-plant').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [259.915, 261.34], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 103, "st": 0, "bm": 0}, {"ddd": 0, "ind": 14, "ty": 4, "nm": "outline 7", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.68], "y": [0]}, "t": 81, "s": [0]}, {"t": 102, "s": [69]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.425, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 31, "s": [214.215, 300, 0], "to": [0, -11.667, 0], "ti": [0, 7, 0]}, {"i": {"x": 0.557, "y": 1}, "o": {"x": 0.49, "y": 0}, "t": 58, "s": [214.215, 230, 0], "to": [0, 8.5, 0], "ti": [-0.333, -12.5, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.68, "y": 0}, "t": 81, "s": [214.215, 258, 0], "to": [8.333, -214, 0], "ti": [-67.333, 34.667, 0]}, {"t": 102, "s": [404.215, -78, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [249.215, 335, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 18, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 35, "s": [100, 116, 100]}, {"t": 45.009765625, "s": [100, 100, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[-20.119, -17.793], [2.973, -18.787], [20.119, 17.793], [-2.973, 18.787]], "o": [[20.119, 17.793], [-18.283, 5.248], [-20.119, -17.793], [18.283, -5.248]], "v": [[18.042, -20.401], [45.078, 39.866], [-18.041, 20.401], [-45.078, -39.866]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 17, "s": [{"i": [[-5.206, -26.349], [13.758, -13.134], [5.206, 26.349], [-13.758, 13.134]], "o": [[5.206, 26.349], [-17.719, -6.915], [-5.206, -26.349], [17.719, 6.915]], "v": [[72.717, -19.278], [57.664, 45.036], [19.283, -8.721], [34.336, -73.036]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 40, "s": [{"i": [[-8.317, -24.564], [11.508, -14.314], [8.317, 24.564], [-11.508, 14.314]], "o": [[8.317, 24.564], [-17.837, -4.378], [-8.317, -24.564], [17.837, 4.378]], "v": [[61.31, -19.513], [55.038, 43.958], [11.496, -2.646], [17.768, -66.116]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 59, "s": [{"i": [[-7.007, -25.928], [12.821, -14.05], [7.007, 25.928], [-12.821, 14.05]], "o": [[7.007, 25.928], [-18.153, -5.679], [-7.007, -25.928], [18.153, 5.679]], "v": [[71.987, -1.105], [61.396, 64.093], [19.405, 13.105], [29.996, -52.093]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 77, "s": [{"i": [[-8.317, -24.564], [11.508, -14.314], [8.317, 24.564], [-11.508, 14.314]], "o": [[8.317, 24.564], [-17.837, -4.378], [-8.317, -24.564], [17.837, 4.378]], "v": [[61.31, -19.513], [55.038, 43.958], [11.496, -2.646], [17.768, -66.116]], "c": true}]}, {"t": 102, "s": [{"i": [[-20.119, -17.793], [2.973, -18.787], [20.119, 17.793], [-2.973, 18.787]], "o": [[20.119, 17.793], [-18.283, 5.248], [-20.119, -17.793], [18.283, -5.248]], "v": [[18.042, -20.401], [45.078, 39.866], [-18.041, 20.401], [-45.078, -39.866]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.071, 0.075, 0.192, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-1827-growing-plant').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 3.5, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 1), comp('wired-lineal-1827-growing-plant').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [181.48, 217.246], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 103, "st": 0, "bm": 0}, {"ddd": 0, "ind": 15, "ty": 4, "nm": "Leaf L", "parent": 14, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [180.765, 216.607, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [174.09, 203.102, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[0.13, -1.01], [0, 0], [0.61, -0.26], [0.02, -0.01], [0.07, -0.02], [0.1, -0.01], [0.5, -0.1], [0.07, 0], [0.37, -0.06], [0.23, -0.03], [0.38, -0.06], [0.04, 0], [3.27, 0.19], [0.05, 0.01], [5.7, 1.88], [0.31, 0.12], [0.79, 0.31], [0.3, 0.12], [1.29, 0.6], [1.23, 0.64], [2.1, 1.32], [3.93, 3.48], [2.37, 2.65], [0.88, 1.08], [0.82, 1.1], [0.39, 0.56], [0.72, 1.13], [0.65, 1.15], [-0.46, 10.36], [-0.03, 0.44], [-0.01, 0.08], [-0.04, 0.38], [-0.03, 0.2], [-0.01, 0.06], [-0.01, 0.02], [-0.72, 0.18], [-0.45, 0.09], [-0.03, 0.01], [-0.37, 0.07], [-0.4, 0.07], [-0.32, 0.04], [-0.25, 0.03], [-0.14, 0.01], [-0.41, 0.04], [-0.67, 0.03], [-0.34, 0.01], [-1.68, -0.11], [-0.05, -0.01], [-0.57, -0.05], [-0.6, -0.08], [0.04, -0.99], [-18.41, -16.3], [-13.28, -2.42]], "o": [[0, 0], [-0.09, 0.66], [-0.02, 0.01], [-0.07, 0.03], [-0.09, 0.02], [-0.49, 0.12], [-0.06, 0.02], [-0.37, 0.08], [-0.22, 0.05], [-0.38, 0.06], [-0.03, 0.01], [-3.06, 0.41], [-0.06, 0.01], [-5.43, -0.3], [-0.32, -0.1], [-0.79, -0.26], [-0.31, -0.1], [-1.29, -0.51], [-1.25, -0.56], [-2.14, -1.1], [-4.21, -2.64], [-2.78, -2.46], [-0.96, -1.06], [-0.89, -1.09], [-0.41, -0.56], [-0.79, -1.12], [-0.72, -1.13], [-6.28, -11.01], [0.01, -0.45], [0.01, -0.09], [0.03, -0.38], [0.02, -0.2], [0, -0.06], [0, -0.02], [0.12, -0.73], [0.44, -0.11], [0.02, -0.01], [0.36, -0.08], [0.39, -0.07], [0.31, -0.05], [0.25, -0.04], [0.15, -0.02], [0.4, -0.05], [0.65, -0.05], [0.33, -0.02], [1.63, -0.05], [0.05, 0], [0.56, 0.03], [0.6, 0.06], [0.98, 0.12], [-0.77, 17.89], [12.37, 10.93], [1.01, 0.19]], "v": [[45.414, 38.578], [45.414, 38.618], [44.284, 40.108], [44.224, 40.128], [44.014, 40.198], [43.734, 40.258], [42.244, 40.598], [42.044, 40.638], [40.934, 40.848], [40.254, 40.968], [39.125, 41.138], [39.014, 41.148], [29.494, 41.488], [29.325, 41.478], [12.534, 38.188], [11.594, 37.868], [9.224, 37.008], [8.314, 36.668], [4.444, 35.008], [0.724, 33.208], [-5.636, 29.578], [-17.886, 20.398], [-25.616, 12.728], [-28.376, 9.508], [-30.936, 6.218], [-32.146, 4.548], [-34.406, 1.178], [-36.466, -2.242], [-45.386, -35.102], [-45.316, -36.432], [-45.296, -36.682], [-45.186, -37.822], [-45.116, -38.422], [-45.096, -38.612], [-45.076, -38.682], [-43.696, -40.192], [-42.376, -40.502], [-42.296, -40.522], [-41.196, -40.752], [-40.006, -40.962], [-39.056, -41.102], [-38.296, -41.202], [-37.856, -41.252], [-36.636, -41.382], [-34.656, -41.512], [-33.656, -41.552], [-28.696, -41.462], [-28.546, -41.452], [-26.856, -41.322], [-25.056, -41.122], [-23.376, -39.152], [4.125, 16.268], [43.855, 36.448]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 17, "s": [{"i": [[0.716, -0.724], [0, 0], [0.643, 0.163], [0.022, 0.004], [0.068, 0.027], [0.086, 0.053], [0.458, 0.224], [0.056, 0.042], [0.331, 0.177], [0.201, 0.116], [0.339, 0.183], [0.032, 0.024], [2.485, 2.133], [0.034, 0.038], [3.394, 4.95], [0.174, 0.283], [0.44, 0.725], [0.167, 0.276], [0.68, 1.25], [0.596, 1.252], [0.872, 2.322], [1.018, 5.15], [0.301, 3.542], [0.047, 1.392], [-0.015, 1.372], [-0.025, 0.682], [-0.113, 1.335], [-0.175, 1.309], [-6.646, 7.96], [-0.291, 0.332], [-0.057, 0.058], [-0.262, 0.278], [-0.145, 0.141], [-0.044, 0.042], [-0.02, 0.01], [-0.682, -0.293], [-0.412, -0.201], [-0.03, -0.01], [-0.337, -0.169], [-0.361, -0.187], [-0.279, -0.162], [-0.217, -0.128], [-0.117, -0.077], [-0.35, -0.217], [-0.551, -0.382], [-0.277, -0.198], [-1.269, -1.106], [-0.034, -0.038], [-0.423, -0.385], [-0.429, -0.427], [0.634, -0.761], [-4.784, -24.119], [-9.038, -10.027]], "o": [[0, 0], [-0.472, 0.47], [-0.022, -0.004], [-0.074, -0.019], [-0.084, -0.039], [-0.462, -0.202], [-0.06, -0.02], [-0.343, -0.161], [-0.205, -0.094], [-0.338, -0.183], [-0.03, -0.01], [-2.682, -1.529], [-0.054, -0.028], [-4.137, -3.53], [-0.194, -0.274], [-0.471, -0.686], [-0.186, -0.267], [-0.717, -1.188], [-0.655, -1.203], [-1.035, -2.172], [-1.748, -4.652], [-0.72, -3.642], [-0.121, -1.425], [-0.047, -1.406], [0.013, -0.694], [0.051, -1.369], [0.113, -1.335], [1.68, -12.563], [0.281, -0.352], [0.062, -0.065], [0.254, -0.284], [0.137, -0.147], [0.036, -0.048], [0.012, -0.016], [0.538, -0.508], [0.416, 0.179], [0.022, 0.004], [0.335, 0.155], [0.352, 0.181], [0.277, 0.148], [0.223, 0.12], [0.131, 0.075], [0.349, 0.203], [0.547, 0.354], [0.274, 0.184], [1.327, 0.948], [0.04, 0.03], [0.427, 0.363], [0.441, 0.411], [0.707, 0.69], [-11.458, 13.761], [3.212, 16.191], [0.688, 0.763]], "v": [[59.452, 45.135], [59.428, 45.167], [57.626, 45.667], [57.566, 45.646], [57.357, 45.575], [57.098, 45.453], [55.706, 44.82], [55.523, 44.73], [54.513, 44.224], [53.899, 43.908], [52.898, 43.358], [52.804, 43.299], [45.027, 37.798], [44.898, 37.687], [33.539, 24.892], [32.985, 24.068], [31.622, 21.947], [31.104, 21.125], [29.033, 17.459], [27.165, 13.772], [24.308, 7.03], [20.13, -7.697], [18.633, -18.483], [18.389, -22.717], [18.348, -26.885], [18.398, -28.947], [18.644, -32.997], [19.079, -36.966], [31.905, -68.507], [32.767, -69.523], [32.934, -69.709], [33.713, -70.549], [34.132, -70.984], [34.263, -71.123], [34.321, -71.167], [36.334, -71.531], [37.572, -70.977], [37.648, -70.945], [38.662, -70.461], [39.736, -69.906], [40.576, -69.442], [41.241, -69.061], [41.621, -68.834], [42.671, -68.197], [44.324, -67.1], [45.144, -66.526], [49.034, -63.448], [49.147, -63.349], [50.412, -62.221], [51.722, -60.971], [51.864, -58.385], [40.139, 2.361], [59.503, 42.495]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 40, "s": [{"i": [[0.594, -0.783], [0, 0], [0.635, 0.075], [0.022, 0.001], [0.068, 0.017], [0.089, 0.04], [0.466, 0.156], [0.058, 0.034], [0.339, 0.127], [0.207, 0.085], [0.347, 0.132], [0.034, 0.019], [2.646, 1.728], [0.037, 0.032], [3.869, 4.309], [0.202, 0.249], [0.512, 0.639], [0.194, 0.243], [0.808, 1.11], [0.728, 1.122], [1.126, 2.111], [1.621, 4.798], [0.733, 3.351], [0.22, 1.326], [0.158, 1.314], [0.062, 0.656], [0.06, 1.291], [-0.003, 1.274], [-5.357, 8.45], [-0.236, 0.354], [-0.047, 0.062], [-0.216, 0.299], [-0.121, 0.153], [-0.037, 0.045], [-0.018, 0.012], [-0.689, -0.195], [-0.42, -0.141], [-0.03, -0.006], [-0.343, -0.119], [-0.368, -0.133], [-0.287, -0.12], [-0.224, -0.095], [-0.122, -0.059], [-0.362, -0.163], [-0.575, -0.296], [-0.29, -0.154], [-1.353, -0.898], [-0.037, -0.032], [-0.453, -0.315], [-0.464, -0.355], [0.511, -0.808], [-7.609, -22.47], [-9.906, -8.455]], "o": [[0, 0], [-0.392, 0.509], [-0.022, -0.001], [-0.073, -0.008], [-0.085, -0.026], [-0.468, -0.135], [-0.06, -0.012], [-0.348, -0.111], [-0.208, -0.064], [-0.347, -0.132], [-0.03, -0.006], [-2.758, -1.125], [-0.055, -0.02], [-4.401, -2.857], [-0.22, -0.237], [-0.536, -0.597], [-0.212, -0.232], [-0.835, -1.046], [-0.777, -1.069], [-1.263, -1.948], [-2.257, -4.23], [-1.146, -3.393], [-0.295, -1.348], [-0.222, -1.339], [-0.075, -0.666], [-0.124, -1.316], [-0.06, -1.291], [0.028, -12.229], [0.224, -0.372], [0.052, -0.07], [0.207, -0.304], [0.113, -0.158], [0.029, -0.05], [0.01, -0.017], [0.451, -0.553], [0.421, 0.119], [0.021, 0.001], [0.34, 0.106], [0.36, 0.129], [0.284, 0.107], [0.228, 0.086], [0.135, 0.055], [0.359, 0.15], [0.568, 0.27], [0.286, 0.142], [1.388, 0.74], [0.042, 0.024], [0.454, 0.294], [0.474, 0.338], [0.763, 0.571], [-9.23, 14.604], [5.108, 15.085], [0.754, 0.644]], "v": [[56.907, 43.704], [56.888, 43.737], [55.227, 44.442], [55.167, 44.43], [54.958, 44.388], [54.695, 44.304], [53.285, 43.873], [53.098, 43.811], [52.068, 43.454], [51.442, 43.228], [50.414, 42.828], [50.317, 42.784], [42.186, 38.499], [42.049, 38.409], [29.575, 27.598], [28.941, 26.879], [27.371, 25.022], [26.772, 24.301], [24.33, 21.054], [22.08, 17.762], [18.499, 11.672], [12.652, -1.891], [9.863, -12.02], [9.098, -16.04], [8.534, -20.022], [8.323, -22], [8.049, -25.906], [7.966, -29.757], [16.27, -61.541], [16.967, -62.62], [17.104, -62.82], [17.743, -63.721], [18.089, -64.19], [18.197, -64.339], [18.248, -64.388], [20.127, -64.99], [21.381, -64.616], [21.457, -64.594], [22.489, -64.259], [23.585, -63.863], [24.448, -63.525], [25.132, -63.244], [25.524, -63.074], [26.607, -62.598], [28.327, -61.756], [29.183, -61.31], [33.291, -58.854], [33.412, -58.774], [34.764, -57.854], [36.175, -56.822], [36.636, -54.367], [33.056, 5.215], [56.624, 41.173]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 59, "s": [{"i": [[0.657, -0.778], [0, 0], [0.653, 0.112], [0.022, 0.002], [0.07, 0.021], [0.09, 0.046], [0.474, 0.187], [0.059, 0.038], [0.343, 0.15], [0.21, 0.099], [0.352, 0.156], [0.034, 0.022], [2.644, 1.933], [0.037, 0.036], [3.77, 4.671], [0.195, 0.269], [0.496, 0.689], [0.188, 0.263], [0.775, 1.193], [0.692, 1.201], [1.051, 2.247], [1.417, 5.055], [0.576, 3.508], [0.155, 1.384], [0.092, 1.369], [0.028, 0.682], [-0.008, 1.34], [-0.072, 1.319], [-6.005, 8.455], [-0.264, 0.353], [-0.052, 0.062], [-0.24, 0.297], [-0.134, 0.152], [-0.041, 0.045], [-0.019, 0.011], [-0.703, -0.239], [-0.427, -0.168], [-0.031, -0.008], [-0.349, -0.142], [-0.374, -0.158], [-0.291, -0.14], [-0.226, -0.11], [-0.123, -0.067], [-0.366, -0.189], [-0.579, -0.338], [-0.291, -0.176], [-1.352, -1.003], [-0.037, -0.035], [-0.452, -0.351], [-0.461, -0.393], [0.573, -0.809], [-6.652, -23.672], [-9.793, -9.291]], "o": [[0, 0], [-0.433, 0.506], [-0.022, -0.002], [-0.075, -0.013], [-0.087, -0.032], [-0.477, -0.165], [-0.061, -0.016], [-0.354, -0.133], [-0.212, -0.077], [-0.352, -0.156], [-0.031, -0.008], [-2.793, -1.315], [-0.056, -0.024], [-4.4, -3.197], [-0.215, -0.258], [-0.523, -0.647], [-0.206, -0.252], [-0.807, -1.128], [-0.747, -1.148], [-1.201, -2.085], [-2.106, -4.501], [-1.002, -3.574], [-0.232, -1.411], [-0.157, -1.398], [-0.041, -0.693], [-0.056, -1.369], [0.008, -1.34], [0.694, -12.656], [0.252, -0.373], [0.057, -0.07], [0.231, -0.303], [0.125, -0.157], [0.033, -0.05], [0.011, -0.017], [0.497, -0.548], [0.429, 0.146], [0.022, 0.002], [0.346, 0.128], [0.366, 0.153], [0.288, 0.126], [0.232, 0.102], [0.137, 0.065], [0.363, 0.175], [0.573, 0.31], [0.288, 0.162], [1.397, 0.842], [0.042, 0.027], [0.454, 0.329], [0.472, 0.376], [0.758, 0.632], [-10.349, 14.613], [4.466, 15.891], [0.745, 0.707]], "v": [[64.197, 63.103], [64.175, 63.137], [62.418, 63.776], [62.357, 63.76], [62.142, 63.705], [61.875, 63.604], [60.438, 63.081], [60.249, 63.006], [59.202, 62.581], [58.566, 62.313], [57.524, 61.843], [57.426, 61.792], [49.243, 56.915], [49.106, 56.814], [36.783, 44.945], [36.167, 44.166], [34.642, 42.158], [34.062, 41.379], [31.711, 37.886], [29.561, 34.356], [26.186, 27.857], [20.872, 13.501], [18.537, 2.865], [17.964, -1.337], [17.597, -5.49], [17.486, -7.549], [17.415, -11.606], [17.539, -15.596], [27.864, -48.042], [28.644, -49.122], [28.797, -49.321], [29.507, -50.219], [29.891, -50.685], [30.011, -50.834], [30.066, -50.882], [32.044, -51.402], [33.322, -50.947], [33.4, -50.921], [34.449, -50.517], [35.562, -50.048], [36.436, -49.651], [37.129, -49.323], [37.526, -49.126], [38.621, -48.574], [40.356, -47.609], [41.217, -47.1], [45.336, -44.335], [45.457, -44.245], [46.806, -43.22], [48.21, -42.076], [48.553, -39.509], [41.604, 21.967], [64.042, 60.468]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.061, "y": 0}, "t": 77, "s": [{"i": [[0.604, -0.778], [0, 0], [0.637, 0.081], [0.022, 0.001], [0.068, 0.018], [0.089, 0.04], [0.467, 0.161], [0.058, 0.034], [0.338, 0.131], [0.207, 0.087], [0.347, 0.135], [0.033, 0.02], [2.637, 1.753], [0.037, 0.033], [3.837, 4.346], [0.2, 0.251], [0.508, 0.643], [0.193, 0.245], [0.799, 1.118], [0.719, 1.129], [1.108, 2.122], [1.576, 4.814], [0.7, 3.358], [0.206, 1.328], [0.145, 1.316], [0.055, 0.656], [0.047, 1.292], [-0.016, 1.275], [-5.467, 8.402], [-0.241, 0.352], [-0.048, 0.062], [-0.22, 0.297], [-0.123, 0.152], [-0.038, 0.045], [-0.018, 0.012], [-0.69, -0.201], [-0.42, -0.145], [-0.03, -0.006], [-0.343, -0.122], [-0.368, -0.137], [-0.287, -0.123], [-0.223, -0.097], [-0.122, -0.06], [-0.362, -0.167], [-0.574, -0.302], [-0.289, -0.157], [-1.349, -0.911], [-0.037, -0.033], [-0.451, -0.32], [-0.462, -0.359], [0.521, -0.804], [-7.398, -22.546], [-9.852, -8.549]], "o": [[0, 0], [-0.399, 0.506], [-0.022, -0.001], [-0.073, -0.009], [-0.085, -0.027], [-0.468, -0.139], [-0.06, -0.013], [-0.348, -0.114], [-0.208, -0.066], [-0.347, -0.135], [-0.03, -0.006], [-2.756, -1.151], [-0.055, -0.021], [-4.387, -2.899], [-0.218, -0.239], [-0.532, -0.602], [-0.21, -0.234], [-0.827, -1.054], [-0.769, -1.076], [-1.247, -1.96], [-2.22, -4.252], [-1.114, -3.404], [-0.282, -1.351], [-0.208, -1.342], [-0.068, -0.666], [-0.11, -1.318], [-0.047, -1.292], [0.158, -12.231], [0.229, -0.37], [0.052, -0.07], [0.211, -0.302], [0.115, -0.157], [0.029, -0.05], [0.01, -0.017], [0.458, -0.549], [0.421, 0.123], [0.022, 0.001], [0.34, 0.109], [0.36, 0.132], [0.283, 0.11], [0.228, 0.089], [0.135, 0.057], [0.359, 0.154], [0.567, 0.275], [0.285, 0.144], [1.386, 0.753], [0.042, 0.024], [0.453, 0.298], [0.472, 0.343], [0.759, 0.578], [-9.419, 14.521], [4.966, 15.135], [0.75, 0.651]], "v": [[55.753, 43.404], [55.734, 43.438], [54.059, 44.127], [53.999, 44.115], [53.79, 44.071], [53.526, 43.984], [52.115, 43.54], [51.929, 43.476], [50.899, 43.109], [50.272, 42.878], [49.246, 42.468], [49.149, 42.423], [41.033, 38.062], [40.896, 37.971], [28.491, 27.041], [27.863, 26.316], [26.306, 24.444], [25.713, 23.717], [23.296, 20.447], [21.073, 17.133], [17.544, 11.008], [11.819, -2.611], [9.128, -12.769], [8.403, -16.797], [7.879, -20.785], [7.688, -22.766], [7.455, -26.674], [7.412, -30.527], [16.085, -62.241], [16.796, -63.314], [16.935, -63.513], [17.587, -64.408], [17.939, -64.874], [18.049, -65.022], [18.1, -65.071], [19.993, -65.655], [21.247, -65.269], [21.324, -65.247], [22.355, -64.902], [23.452, -64.496], [24.314, -64.149], [24.998, -63.862], [25.389, -63.689], [26.472, -63.202], [28.189, -62.344], [29.043, -61.89], [33.141, -59.395], [33.261, -59.314], [34.608, -58.381], [36.013, -57.337], [36.449, -54.877], [32.223, 4.686], [55.496, 40.87]], "c": true}]}, {"t": 102, "s": [{"i": [[0.13, -1.01], [0, 0], [0.61, -0.26], [0.02, -0.01], [0.07, -0.02], [0.1, -0.01], [0.5, -0.1], [0.07, 0], [0.37, -0.06], [0.23, -0.03], [0.38, -0.06], [0.04, 0], [3.27, 0.19], [0.05, 0.01], [5.7, 1.88], [0.31, 0.12], [0.79, 0.31], [0.3, 0.12], [1.29, 0.6], [1.23, 0.64], [2.1, 1.32], [3.93, 3.48], [2.37, 2.65], [0.88, 1.08], [0.82, 1.1], [0.39, 0.56], [0.72, 1.13], [0.65, 1.15], [-0.46, 10.36], [-0.03, 0.44], [-0.01, 0.08], [-0.04, 0.38], [-0.03, 0.2], [-0.01, 0.06], [-0.01, 0.02], [-0.72, 0.18], [-0.45, 0.09], [-0.03, 0.01], [-0.37, 0.07], [-0.4, 0.07], [-0.32, 0.04], [-0.25, 0.03], [-0.14, 0.01], [-0.41, 0.04], [-0.67, 0.03], [-0.34, 0.01], [-1.68, -0.11], [-0.05, -0.01], [-0.57, -0.05], [-0.6, -0.08], [0.04, -0.99], [-18.41, -16.3], [-13.28, -2.42]], "o": [[0, 0], [-0.09, 0.66], [-0.02, 0.01], [-0.07, 0.03], [-0.09, 0.02], [-0.49, 0.12], [-0.06, 0.02], [-0.37, 0.08], [-0.22, 0.05], [-0.38, 0.06], [-0.03, 0.01], [-3.06, 0.41], [-0.06, 0.01], [-5.43, -0.3], [-0.32, -0.1], [-0.79, -0.26], [-0.31, -0.1], [-1.29, -0.51], [-1.25, -0.56], [-2.14, -1.1], [-4.21, -2.64], [-2.78, -2.46], [-0.96, -1.06], [-0.89, -1.09], [-0.41, -0.56], [-0.79, -1.12], [-0.72, -1.13], [-6.28, -11.01], [0.01, -0.45], [0.01, -0.09], [0.03, -0.38], [0.02, -0.2], [0, -0.06], [0, -0.02], [0.12, -0.73], [0.44, -0.11], [0.02, -0.01], [0.36, -0.08], [0.39, -0.07], [0.31, -0.05], [0.25, -0.04], [0.15, -0.02], [0.4, -0.05], [0.65, -0.05], [0.33, -0.02], [1.63, -0.05], [0.05, 0], [0.56, 0.03], [0.6, 0.06], [0.98, 0.12], [-0.77, 17.89], [12.37, 10.93], [1.01, 0.19]], "v": [[45.414, 38.578], [45.414, 38.618], [44.284, 40.108], [44.224, 40.128], [44.014, 40.198], [43.734, 40.258], [42.244, 40.598], [42.044, 40.638], [40.934, 40.848], [40.254, 40.968], [39.125, 41.138], [39.014, 41.148], [29.494, 41.488], [29.325, 41.478], [12.534, 38.188], [11.594, 37.868], [9.224, 37.008], [8.314, 36.668], [4.444, 35.008], [0.724, 33.208], [-5.636, 29.578], [-17.886, 20.398], [-25.616, 12.728], [-28.376, 9.508], [-30.936, 6.218], [-32.146, 4.548], [-34.406, 1.178], [-36.466, -2.242], [-45.386, -35.102], [-45.316, -36.432], [-45.296, -36.682], [-45.186, -37.822], [-45.116, -38.422], [-45.096, -38.612], [-45.076, -38.682], [-43.696, -40.192], [-42.376, -40.502], [-42.296, -40.522], [-41.196, -40.752], [-40.006, -40.962], [-39.056, -41.102], [-38.296, -41.202], [-37.856, -41.252], [-36.636, -41.382], [-34.656, -41.512], [-33.656, -41.552], [-28.696, -41.462], [-28.546, -41.452], [-26.856, -41.322], [-25.056, -41.122], [-23.376, -39.152], [4.125, 16.268], [43.855, 36.448]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.173, 0.647, 0.553, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-1827-growing-plant').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [173.926, 203.102], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 50, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 1, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[2.36, -18.57], [0.07, -0.41], [0.32, -0.07], [0.02, -0.01], [0.16, -0.04], [0.5, -0.1], [0.07, 0], [0.37, -0.06], [0.23, -0.03], [0.38, -0.06], [0.04, 0], [3.27, 0.18], [0.05, 0.01], [5.7, 1.88], [0.31, 0.12], [0.79, 0.31], [0.3, 0.12], [1.29, 0.6], [1.23, 0.64], [2.1, 1.31], [3.93, 3.48], [2.37, 2.65], [0.89, 1.08], [0.82, 1.11], [0.39, 0.56], [0.72, 1.13], [0.65, 1.15], [-0.46, 10.36], [-0.03, 0.44], [-0.01, 0.08], [-0.04, 0.38], [-0.03, 0.2], [-0.01, 0.06], [-0.01, 0.02], [-0.72, 0.18], [-0.45, 0.09], [-0.03, 0.01], [-0.37, 0.07], [-0.4, 0.07], [-0.32, 0.04], [-0.25, 0.03], [-0.14, 0.01], [-0.41, 0.04], [-0.67, 0.03], [-0.34, 0.01], [-0.828, -0.015], [-0.841, -0.055], [-0.05, -0.01], [-0.57, -0.05], [-0.6, -0.08], [-13.46, -11.9]], "o": [[-0.05, 0.42], [-0.32, 0.09], [-0.02, 0.01], [-0.16, 0.05], [-0.49, 0.12], [-0.06, 0.02], [-0.37, 0.08], [-0.22, 0.05], [-0.38, 0.06], [-0.03, 0.01], [-3.06, 0.41], [-0.06, 0.01], [-5.43, -0.31], [-0.32, -0.1], [-0.79, -0.26], [-0.31, -0.1], [-1.29, -0.51], [-1.25, -0.56], [-2.14, -1.1], [-4.21, -2.64], [-2.78, -2.46], [-0.95, -1.06], [-0.88, -1.09], [-0.41, -0.56], [-0.78, -1.12], [-0.72, -1.13], [-6.28, -11.01], [0.01, -0.45], [0.01, -0.09], [0.03, -0.38], [0.02, -0.2], [0, -0.06], [0, -0.02], [0.12, -0.73], [0.44, -0.11], [0.02, -0.01], [0.36, -0.08], [0.39, -0.07], [0.31, -0.05], [0.25, -0.04], [0.15, -0.02], [0.4, -0.05], [0.65, -0.05], [0.33, -0.02], [0.81, -0.025], [0.827, 0.015], [0.05, 0], [0.56, 0.03], [0.6, 0.06], [14.27, 1.78], [19.68, 17.4]], "v": [[45.251, 38.62], [45.081, 39.86], [44.121, 40.11], [44.061, 40.13], [43.571, 40.26], [42.081, 40.6], [41.881, 40.64], [40.771, 40.85], [40.091, 40.97], [38.961, 41.14], [38.851, 41.15], [29.331, 41.49], [29.161, 41.48], [12.371, 38.19], [11.431, 37.87], [9.061, 37.01], [8.151, 36.67], [4.281, 35.01], [0.561, 33.21], [-5.799, 29.58], [-18.049, 20.4], [-25.779, 12.73], [-28.539, 9.51], [-31.099, 6.22], [-32.309, 4.55], [-34.569, 1.18], [-36.629, -2.24], [-45.549, -35.1], [-45.479, -36.43], [-45.459, -36.68], [-45.349, -37.82], [-45.279, -38.42], [-45.259, -38.61], [-45.239, -38.68], [-43.859, -40.19], [-42.539, -40.5], [-42.459, -40.52], [-41.359, -40.75], [-40.169, -40.96], [-39.219, -41.1], [-38.459, -41.2], [-38.019, -41.25], [-36.799, -41.38], [-34.819, -41.51], [-33.819, -41.55], [-31.361, -41.565], [-28.859, -41.46], [-28.709, -41.45], [-27.019, -41.32], [-25.219, -41.12], [18.041, -20.4]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 17, "s": [{"i": [[13.134, -13.338], [0.304, -0.284], [0.297, 0.138], [0.022, 0.004], [0.151, 0.065], [0.458, 0.224], [0.056, 0.042], [0.331, 0.177], [0.201, 0.116], [0.339, 0.183], [0.032, 0.024], [2.492, 2.126], [0.034, 0.038], [3.394, 4.95], [0.174, 0.283], [0.44, 0.725], [0.167, 0.276], [0.68, 1.25], [0.596, 1.252], [0.871, 2.317], [1.018, 5.15], [0.282, 3.544], [0.039, 1.399], [-0.021, 1.38], [-0.029, 0.682], [-0.113, 1.335], [-0.175, 1.309], [-6.646, 7.96], [-0.291, 0.332], [-0.057, 0.058], [-0.262, 0.278], [-0.145, 0.141], [-0.044, 0.042], [-0.02, 0.01], [-0.682, -0.293], [-0.412, -0.201], [-0.03, -0.01], [-0.337, -0.169], [-0.361, -0.187], [-0.279, -0.162], [-0.217, -0.128], [-0.117, -0.077], [-0.35, -0.217], [-0.551, -0.382], [-0.276, -0.198], [-0.649, -0.513], [-0.636, -0.554], [-0.034, -0.038], [-0.423, -0.385], [-0.432, -0.424], [-3.49, -17.624]], "o": [[-0.294, 0.304], [-0.309, -0.122], [-0.022, -0.004], [-0.158, -0.057], [-0.462, -0.202], [-0.06, -0.02], [-0.343, -0.161], [-0.205, -0.094], [-0.338, -0.183], [-0.03, -0.01], [-2.682, -1.529], [-0.054, -0.028], [-4.131, -3.538], [-0.194, -0.274], [-0.471, -0.686], [-0.186, -0.267], [-0.717, -1.188], [-0.655, -1.203], [-1.035, -2.172], [-1.748, -4.652], [-0.72, -3.642], [-0.113, -1.419], [-0.039, -1.4], [0.013, -0.694], [0.059, -1.363], [0.113, -1.335], [1.68, -12.563], [0.281, -0.352], [0.062, -0.065], [0.254, -0.284], [0.137, -0.147], [0.036, -0.048], [0.012, -0.016], [0.538, -0.508], [0.416, 0.179], [0.022, 0.004], [0.335, 0.155], [0.352, 0.181], [0.277, 0.148], [0.223, 0.12], [0.131, 0.075], [0.349, 0.203], [0.547, 0.354], [0.274, 0.184], [0.66, 0.471], [0.649, 0.513], [0.04, 0.03], [0.427, 0.363], [0.441, 0.411], [10.27, 10.066], [5.103, 25.769]], "v": [[59.264, 45.169], [58.377, 46.052], [57.462, 45.669], [57.402, 45.648], [56.934, 45.455], [55.543, 44.822], [55.359, 44.733], [54.349, 44.227], [53.736, 43.91], [52.734, 43.36], [52.641, 43.301], [44.863, 37.801], [44.734, 37.69], [33.375, 24.895], [32.822, 24.07], [31.458, 21.95], [30.941, 21.128], [28.869, 17.461], [27.002, 13.775], [24.144, 7.032], [19.967, -7.695], [18.469, -18.481], [18.226, -22.715], [18.184, -26.883], [18.234, -28.945], [18.48, -32.995], [18.915, -36.964], [31.741, -68.505], [32.603, -69.52], [32.77, -69.707], [33.549, -70.547], [33.968, -70.982], [34.099, -71.121], [34.158, -71.164], [36.171, -71.529], [37.408, -70.975], [37.484, -70.942], [38.498, -70.458], [39.572, -69.904], [40.412, -69.439], [41.078, -69.058], [41.458, -68.831], [42.507, -68.195], [44.16, -67.098], [44.98, -66.524], [46.944, -65.046], [48.87, -63.445], [48.983, -63.346], [50.248, -62.219], [51.559, -60.968], [73.403, -18.265]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 40, "s": [{"i": [[10.887, -14.41], [0.255, -0.309], [0.301, 0.095], [0.022, 0.001], [0.153, 0.043], [0.466, 0.156], [0.058, 0.034], [0.339, 0.127], [0.207, 0.085], [0.347, 0.132], [0.034, 0.019], [2.651, 1.72], [0.037, 0.032], [3.869, 4.309], [0.202, 0.249], [0.512, 0.639], [0.194, 0.243], [0.808, 1.11], [0.728, 1.122], [1.124, 2.107], [1.621, 4.798], [0.716, 3.355], [0.213, 1.333], [0.154, 1.322], [0.058, 0.656], [0.06, 1.291], [-0.003, 1.274], [-5.357, 8.45], [-0.236, 0.354], [-0.047, 0.062], [-0.216, 0.299], [-0.121, 0.153], [-0.037, 0.045], [-0.018, 0.012], [-0.689, -0.195], [-0.42, -0.141], [-0.03, -0.006], [-0.343, -0.119], [-0.368, -0.133], [-0.287, -0.12], [-0.224, -0.095], [-0.122, -0.059], [-0.362, -0.163], [-0.575, -0.296], [-0.289, -0.155], [-0.685, -0.41], [-0.678, -0.45], [-0.037, -0.032], [-0.453, -0.315], [-0.467, -0.351], [-5.554, -16.42]], "o": [[-0.243, 0.328], [-0.311, -0.078], [-0.022, -0.001], [-0.158, -0.035], [-0.468, -0.135], [-0.06, -0.012], [-0.348, -0.111], [-0.208, -0.064], [-0.347, -0.132], [-0.03, -0.006], [-2.758, -1.125], [-0.055, -0.02], [-4.396, -2.865], [-0.22, -0.237], [-0.536, -0.597], [-0.212, -0.232], [-0.835, -1.046], [-0.777, -1.069], [-1.263, -1.948], [-2.257, -4.23], [-1.146, -3.393], [-0.286, -1.343], [-0.213, -1.335], [-0.075, -0.666], [-0.115, -1.312], [-0.06, -1.291], [0.028, -12.229], [0.224, -0.372], [0.052, -0.07], [0.207, -0.304], [0.113, -0.158], [0.029, -0.05], [0.01, -0.017], [0.451, -0.553], [0.421, 0.119], [0.021, 0.001], [0.34, 0.106], [0.36, 0.129], [0.284, 0.107], [0.228, 0.086], [0.135, 0.055], [0.359, 0.15], [0.568, 0.27], [0.286, 0.142], [0.69, 0.368], [0.685, 0.41], [0.042, 0.024], [0.454, 0.294], [0.474, 0.338], [11.089, 8.338], [8.121, 24.008]], "v": [[56.724, 43.74], [55.987, 44.696], [55.064, 44.445], [55.004, 44.432], [54.531, 44.306], [53.121, 43.876], [52.935, 43.813], [51.905, 43.456], [51.278, 43.23], [50.251, 42.83], [50.154, 42.786], [42.023, 38.502], [41.885, 38.412], [29.411, 27.6], [28.778, 26.881], [27.207, 25.024], [26.609, 24.303], [24.166, 21.056], [21.916, 17.764], [18.335, 11.674], [12.488, -1.888], [9.699, -12.018], [8.934, -16.037], [8.371, -20.02], [8.159, -21.998], [7.885, -25.903], [7.802, -29.754], [16.106, -61.538], [16.803, -62.618], [16.94, -62.818], [17.579, -63.719], [17.926, -64.187], [18.034, -64.337], [18.084, -64.386], [19.964, -64.987], [21.217, -64.613], [21.294, -64.592], [22.325, -64.256], [23.422, -63.861], [24.284, -63.522], [24.968, -63.241], [25.36, -63.072], [26.444, -62.595], [28.163, -61.754], [29.019, -61.307], [31.084, -60.14], [33.128, -58.852], [33.249, -58.771], [34.6, -57.851], [36.011, -56.82], [62.275, -18.717]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 59, "s": [{"i": [[12.053, -14.322], [0.281, -0.306], [0.307, 0.115], [0.022, 0.002], [0.156, 0.053], [0.474, 0.187], [0.059, 0.038], [0.343, 0.15], [0.21, 0.099], [0.352, 0.156], [0.034, 0.022], [2.65, 1.925], [0.037, 0.036], [3.77, 4.671], [0.195, 0.269], [0.496, 0.689], [0.188, 0.263], [0.775, 1.193], [0.692, 1.201], [1.049, 2.242], [1.417, 5.055], [0.558, 3.511], [0.148, 1.392], [0.087, 1.377], [0.024, 0.682], [-0.008, 1.34], [-0.072, 1.319], [-6.005, 8.455], [-0.264, 0.353], [-0.052, 0.062], [-0.24, 0.297], [-0.134, 0.152], [-0.041, 0.045], [-0.019, 0.011], [-0.703, -0.239], [-0.427, -0.168], [-0.031, -0.008], [-0.349, -0.142], [-0.374, -0.158], [-0.291, -0.14], [-0.226, -0.11], [-0.123, -0.067], [-0.366, -0.189], [-0.579, -0.338], [-0.291, -0.176], [-0.687, -0.461], [-0.677, -0.502], [-0.037, -0.035], [-0.452, -0.351], [-0.464, -0.389], [-4.855, -17.298]], "o": [[-0.27, 0.326], [-0.318, -0.098], [-0.022, -0.002], [-0.161, -0.045], [-0.477, -0.165], [-0.061, -0.016], [-0.354, -0.133], [-0.212, -0.077], [-0.352, -0.156], [-0.031, -0.008], [-2.793, -1.315], [-0.056, -0.024], [-4.394, -3.205], [-0.215, -0.258], [-0.523, -0.647], [-0.206, -0.252], [-0.807, -1.128], [-0.747, -1.148], [-1.201, -2.085], [-2.106, -4.501], [-1.002, -3.574], [-0.223, -1.406], [-0.148, -1.393], [-0.041, -0.693], [-0.048, -1.364], [0.008, -1.34], [0.694, -12.656], [0.252, -0.373], [0.057, -0.07], [0.231, -0.303], [0.125, -0.157], [0.033, -0.05], [0.011, -0.017], [0.497, -0.548], [0.429, 0.146], [0.022, 0.002], [0.346, 0.128], [0.366, 0.153], [0.288, 0.126], [0.232, 0.102], [0.137, 0.065], [0.363, 0.175], [0.573, 0.31], [0.288, 0.162], [0.694, 0.418], [0.687, 0.461], [0.042, 0.027], [0.454, 0.329], [0.472, 0.376], [11.024, 9.234], [7.099, 25.292]], "v": [[64.012, 63.139], [63.197, 64.089], [62.254, 63.778], [62.193, 63.762], [61.711, 63.606], [60.275, 63.083], [60.085, 63.009], [59.038, 62.583], [58.402, 62.315], [57.361, 61.845], [57.263, 61.794], [49.08, 56.917], [48.942, 56.816], [36.62, 44.947], [36.003, 44.168], [34.478, 42.161], [33.898, 41.382], [31.547, 37.888], [29.398, 34.358], [26.022, 27.859], [20.708, 13.503], [18.373, 2.867], [17.8, -1.335], [17.434, -5.487], [17.323, -7.547], [17.251, -11.604], [17.375, -15.594], [27.701, -48.04], [28.481, -49.12], [28.633, -49.319], [29.344, -50.217], [29.728, -50.683], [29.848, -50.832], [29.902, -50.88], [31.881, -51.4], [33.158, -50.945], [33.236, -50.918], [34.285, -50.515], [35.399, -50.046], [36.273, -49.649], [36.965, -49.32], [37.362, -49.124], [38.458, -48.571], [40.192, -47.607], [41.054, -47.098], [43.127, -45.778], [45.172, -44.333], [45.293, -44.243], [46.642, -43.217], [48.046, -42.073], [73.156, -1.205]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.061, "y": 0}, "t": 77, "s": [{"i": [[11.081, -14.312], [0.26, -0.307], [0.302, 0.098], [0.022, 0.001], [0.153, 0.045], [0.467, 0.161], [0.058, 0.034], [0.338, 0.131], [0.207, 0.087], [0.347, 0.135], [0.033, 0.02], [2.642, 1.745], [0.037, 0.033], [3.837, 4.346], [0.2, 0.251], [0.508, 0.643], [0.193, 0.245], [0.799, 1.118], [0.719, 1.129], [1.106, 2.118], [1.576, 4.814], [0.683, 3.362], [0.2, 1.336], [0.14, 1.324], [0.051, 0.657], [0.047, 1.292], [-0.016, 1.275], [-5.467, 8.402], [-0.241, 0.352], [-0.048, 0.062], [-0.22, 0.297], [-0.123, 0.152], [-0.038, 0.045], [-0.018, 0.012], [-0.69, -0.201], [-0.42, -0.145], [-0.03, -0.006], [-0.343, -0.122], [-0.368, -0.137], [-0.287, -0.123], [-0.223, -0.097], [-0.122, -0.06], [-0.362, -0.167], [-0.574, -0.302], [-0.289, -0.158], [-0.683, -0.416], [-0.675, -0.456], [-0.037, -0.033], [-0.451, -0.32], [-0.465, -0.355], [-5.4, -16.475]], "o": [[-0.248, 0.325], [-0.311, -0.081], [-0.022, -0.001], [-0.158, -0.036], [-0.468, -0.139], [-0.06, -0.013], [-0.348, -0.114], [-0.208, -0.066], [-0.347, -0.135], [-0.03, -0.006], [-2.756, -1.151], [-0.055, -0.021], [-4.382, -2.907], [-0.218, -0.239], [-0.532, -0.602], [-0.21, -0.234], [-0.827, -1.054], [-0.769, -1.076], [-1.247, -1.96], [-2.22, -4.252], [-1.114, -3.404], [-0.273, -1.346], [-0.2, -1.337], [-0.068, -0.666], [-0.102, -1.313], [-0.047, -1.292], [0.158, -12.231], [0.229, -0.37], [0.052, -0.07], [0.211, -0.302], [0.115, -0.157], [0.029, -0.05], [0.01, -0.017], [0.458, -0.549], [0.421, 0.123], [0.022, 0.001], [0.34, 0.109], [0.36, 0.132], [0.283, 0.11], [0.228, 0.089], [0.135, 0.057], [0.359, 0.154], [0.567, 0.275], [0.285, 0.144], [0.689, 0.374], [0.683, 0.416], [0.042, 0.024], [0.453, 0.298], [0.472, 0.343], [11.042, 8.443], [7.896, 24.089]], "v": [[55.57, 43.44], [54.82, 44.39], [53.896, 44.13], [53.836, 44.117], [53.363, 43.986], [51.952, 43.542], [51.765, 43.478], [50.735, 43.112], [50.109, 42.88], [49.082, 42.47], [48.985, 42.425], [40.869, 38.064], [40.732, 37.973], [28.327, 27.043], [27.699, 26.318], [26.143, 24.446], [25.55, 23.719], [23.133, 20.449], [20.91, 17.136], [17.38, 11.01], [11.655, -2.609], [8.964, -12.767], [8.239, -16.794], [7.716, -20.783], [7.525, -22.764], [7.291, -26.672], [7.249, -30.525], [15.921, -62.239], [16.632, -63.312], [16.772, -63.51], [17.423, -64.406], [17.776, -64.871], [17.885, -65.02], [17.937, -65.068], [19.83, -65.653], [21.084, -65.267], [21.16, -65.245], [22.192, -64.899], [23.288, -64.494], [24.15, -64.147], [24.834, -63.86], [25.226, -63.687], [26.308, -63.2], [28.025, -62.342], [28.88, -61.888], [30.939, -60.701], [32.977, -59.393], [33.097, -59.312], [34.445, -58.379], [35.849, -57.335], [61.805, -18.979]], "c": true}]}, {"t": 102, "s": [{"i": [[2.36, -18.57], [0.07, -0.41], [0.32, -0.07], [0.02, -0.01], [0.16, -0.04], [0.5, -0.1], [0.07, 0], [0.37, -0.06], [0.23, -0.03], [0.38, -0.06], [0.04, 0], [3.27, 0.18], [0.05, 0.01], [5.7, 1.88], [0.31, 0.12], [0.79, 0.31], [0.3, 0.12], [1.29, 0.6], [1.23, 0.64], [2.1, 1.31], [3.93, 3.48], [2.37, 2.65], [0.89, 1.08], [0.82, 1.11], [0.39, 0.56], [0.72, 1.13], [0.65, 1.15], [-0.46, 10.36], [-0.03, 0.44], [-0.01, 0.08], [-0.04, 0.38], [-0.03, 0.2], [-0.01, 0.06], [-0.01, 0.02], [-0.72, 0.18], [-0.45, 0.09], [-0.03, 0.01], [-0.37, 0.07], [-0.4, 0.07], [-0.32, 0.04], [-0.25, 0.03], [-0.14, 0.01], [-0.41, 0.04], [-0.67, 0.03], [-0.34, 0.01], [-0.828, -0.015], [-0.841, -0.055], [-0.05, -0.01], [-0.57, -0.05], [-0.6, -0.08], [-13.46, -11.9]], "o": [[-0.05, 0.42], [-0.32, 0.09], [-0.02, 0.01], [-0.16, 0.05], [-0.49, 0.12], [-0.06, 0.02], [-0.37, 0.08], [-0.22, 0.05], [-0.38, 0.06], [-0.03, 0.01], [-3.06, 0.41], [-0.06, 0.01], [-5.43, -0.31], [-0.32, -0.1], [-0.79, -0.26], [-0.31, -0.1], [-1.29, -0.51], [-1.25, -0.56], [-2.14, -1.1], [-4.21, -2.64], [-2.78, -2.46], [-0.95, -1.06], [-0.88, -1.09], [-0.41, -0.56], [-0.78, -1.12], [-0.72, -1.13], [-6.28, -11.01], [0.01, -0.45], [0.01, -0.09], [0.03, -0.38], [0.02, -0.2], [0, -0.06], [0, -0.02], [0.12, -0.73], [0.44, -0.11], [0.02, -0.01], [0.36, -0.08], [0.39, -0.07], [0.31, -0.05], [0.25, -0.04], [0.15, -0.02], [0.4, -0.05], [0.65, -0.05], [0.33, -0.02], [0.81, -0.025], [0.827, 0.015], [0.05, 0], [0.56, 0.03], [0.6, 0.06], [14.27, 1.78], [19.68, 17.4]], "v": [[45.251, 38.62], [45.081, 39.86], [44.121, 40.11], [44.061, 40.13], [43.571, 40.26], [42.081, 40.6], [41.881, 40.64], [40.771, 40.85], [40.091, 40.97], [38.961, 41.14], [38.851, 41.15], [29.331, 41.49], [29.161, 41.48], [12.371, 38.19], [11.431, 37.87], [9.061, 37.01], [8.151, 36.67], [4.281, 35.01], [0.561, 33.21], [-5.799, 29.58], [-18.049, 20.4], [-25.779, 12.73], [-28.539, 9.51], [-31.099, 6.22], [-32.309, 4.55], [-34.569, 1.18], [-36.629, -2.24], [-45.549, -35.1], [-45.479, -36.43], [-45.459, -36.68], [-45.349, -37.82], [-45.279, -38.42], [-45.259, -38.61], [-45.239, -38.68], [-43.859, -40.19], [-42.539, -40.5], [-42.459, -40.52], [-41.359, -40.75], [-40.169, -40.96], [-39.219, -41.1], [-38.459, -41.2], [-38.019, -41.25], [-36.799, -41.38], [-34.819, -41.51], [-33.819, -41.55], [-31.361, -41.565], [-28.859, -41.46], [-28.709, -41.45], [-27.019, -41.32], [-25.219, -41.12], [18.041, -20.4]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.173, 0.647, 0.553, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-1827-growing-plant').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [174.09, 203.1], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 103, "st": 0, "bm": 0}, {"ddd": 0, "ind": 16, "ty": 4, "nm": "outline", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.68], "y": [0]}, "t": 81, "s": [0]}, {"t": 102, "s": [69]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.425, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 31, "s": [214.215, 300, 0], "to": [0, -11.667, 0], "ti": [0, 7, 0]}, {"i": {"x": 0.557, "y": 1}, "o": {"x": 0.49, "y": 0}, "t": 58, "s": [214.215, 230, 0], "to": [0, 8.5, 0], "ti": [-0.333, -12.5, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.68, "y": 0}, "t": 81, "s": [214.215, 258, 0], "to": [8.333, -214, 0], "ti": [-67.333, 34.667, 0]}, {"t": 102, "s": [404.215, -78, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [249.215, 335, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 18, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 35, "s": [100, 116, 100]}, {"t": 45.009765625, "s": [100, 100, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[24.369, -27.556], [25.731, 4.072], [-24.369, 27.555], [-25.731, -4.072]], "o": [[-24.369, 27.555], [-7.188, -25.04], [24.369, -27.556], [7.188, 25.04]], "v": [[27.941, 24.71], [-54.601, 61.74], [-27.941, -24.71], [54.601, -61.74]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 17, "s": [{"i": [[2.423, -36.705], [22.847, -12.518], [-2.423, 36.706], [-22.847, 12.518]], "o": [[-2.423, 36.705], [-21.003, -15.413], [2.423, -36.706], [21.003, 15.413]], "v": [[-40.514, -24.246], [-83.162, 55.539], [-114.952, -29.159], [-72.304, -108.944]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 40, "s": [{"i": [[7.002, -30.424], [23.449, -7.919], [-7.002, 30.424], [-23.449, 7.919]], "o": [[-7.002, 30.424], [-18.121, -15.232], [7.002, -30.424], [18.121, 15.232]], "v": [[-26.232, -5.127], [-77.203, 56.833], [-96.799, -17.542], [-45.828, -79.501]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 59, "s": [{"i": [[2.423, -32.323], [22.847, -11.024], [-2.423, 32.324], [-22.847, 11.024]], "o": [[-2.423, 32.323], [-21.003, -13.573], [2.423, -32.324], [21.003, 13.573]], "v": [[-40.818, 5.279], [-83.466, 75.539], [-115.256, 0.952], [-72.608, -69.307]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 77, "s": [{"i": [[7.002, -34.796], [23.449, -9.057], [-7.002, 34.797], [-23.449, 9.057]], "o": [[-7.002, 34.796], [-18.121, -17.421], [7.002, -34.797], [18.121, 17.421]], "v": [[-26.232, -14.032], [-77.203, 56.833], [-96.799, -28.231], [-45.828, -99.096]], "c": true}]}, {"t": 102, "s": [{"i": [[24.369, -27.556], [25.731, 4.072], [-24.369, 27.555], [-25.731, -4.072]], "o": [[-24.369, 27.555], [-7.188, -25.04], [24.369, -27.556], [7.188, 25.04]], "v": [[27.941, 24.71], [-54.601, 61.74], [-27.941, -24.71], [54.601, -61.74]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.071, 0.075, 0.192, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-1827-growing-plant').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 3.5, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 1), comp('wired-lineal-1827-growing-plant').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [339.685, 180.231], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 103, "st": 0, "bm": 0}, {"ddd": 0, "ind": 17, "ty": 4, "nm": "Leaf R", "parent": 16, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [340.313, 179.594, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [332.293, 166.09, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[-0.44, -0.44], [17.71, -20.02], [-6.94, -24.99], [0, 0], [0.13, -0.02], [0.4, -0.06], [0.53, -0.07], [0.26, -0.03], [0.56, -0.06], [0.08, 0], [3.71, 0.29], [0.39, 0.04], [1.13, 0.18], [0, 0], [-24.37, 27.55], [0, 0], [-2.12, 2.08], [-21.96, -0.41], [-0.33, 0], [-0.46, -0.03], [-0.39, -0.03], [-0.33, -0.03], [-0.53, -0.07]], "o": [[-20.56, 3.83], [-24.22, 27.38], [0, 0], [-0.13, 0.02], [-0.41, 0.07], [-0.54, 0.09], [-0.25, 0.04], [-0.56, 0.08], [-0.07, 0.01], [-3.89, 0.4], [-0.39, -0.03], [-1.15, -0.11], [0, 0], [-7.19, -25.04], [0, 0], [2.02, -2.28], [20.86, -20.55], [0.32, 0.01], [0.46, 0.02], [0.39, 0.02], [0.33, 0.02], [0.54, 0.05], [0.66, 0.09]], "v": [[55.622, -61.19], [-4.608, -24.71], [-31.398, 61.26], [-31.398, 61.27], [-31.788, 61.34], [-32.998, 61.54], [-34.598, 61.78], [-35.368, 61.88], [-37.038, 62.08], [-37.258, 62.1], [-48.688, 62.27], [-49.858, 62.17], [-53.278, 61.74], [-53.278, 61.73], [-26.618, -24.71], [-26.618, -24.72], [-20.408, -31.26], [47.822, -62.44], [48.792, -62.42], [50.172, -62.35], [51.342, -62.27], [52.322, -62.19], [53.932, -62.01]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 17, "s": [{"i": [[-0.616, -0.087], [2.127, -26.644], [-20.563, -15.806], [0, 0], [0.092, -0.094], [0.284, -0.288], [0.382, -0.374], [0.19, -0.18], [0.412, -0.384], [0.064, -0.048], [3.14, -1.997], [0.335, -0.205], [1.011, -0.535], [0, 0], [-2.927, 36.665], [0, 0], [-0.439, 2.937], [-17.8, 12.867], [-0.264, 0.198], [-0.386, 0.252], [-0.33, 0.21], [-0.282, 0.174], [-0.466, 0.263]], "o": [[-14.133, 15.415], [-2.909, 36.439], [0, 0], [-0.092, 0.094], [-0.286, 0.302], [-0.378, 0.396], [-0.176, 0.182], [-0.4, 0.401], [-0.05, 0.05], [-2.869, 2.657], [-0.33, 0.21], [-0.985, 0.603], [0, 0], [-20.793, -15.696], [0, 0], [0.245, -3.036], [4.327, -28.961], [0.262, -0.184], [0.38, -0.26], [0.324, -0.218], [0.276, -0.182], [0.462, -0.284], [0.582, -0.325]], "v": [[-69.983, -107.12], [-96.209, -41.77], [-65.969, 43.048], [-65.963, 43.056], [-66.233, 43.346], [-67.08, 44.233], [-68.214, 45.386], [-68.77, 45.929], [-69.985, 47.092], [-70.148, 47.24], [-79.183, 54.244], [-80.178, 54.867], [-83.17, 56.578], [-83.176, 56.57], [-113.803, -28.545], [-113.809, -28.553], [-112.775, -37.513], [-76.969, -103.433], [-76.182, -104], [-75.037, -104.773], [-74.053, -105.412], [-73.222, -105.937], [-71.827, -106.76]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 40, "s": [{"i": [[-0.584, -0.133], [4.956, -22.19], [-18.1, -15.262], [0, 0], [0.099, -0.07], [0.305, -0.215], [0.409, -0.278], [0.203, -0.133], [0.439, -0.284], [0.067, -0.034], [3.245, -1.377], [0.345, -0.14], [1.033, -0.352], [0, 0], [-6.82, 30.536], [0, 0], [-0.743, 2.427], [-18.563, 9.104], [-0.276, 0.141], [-0.399, 0.175], [-0.341, 0.145], [-0.291, 0.119], [-0.478, 0.176]], "o": [[-15.306, 11.598], [-6.778, 30.348], [0, 0], [-0.099, 0.07], [-0.308, 0.227], [-0.407, 0.297], [-0.189, 0.136], [-0.429, 0.298], [-0.054, 0.037], [-3.056, 1.958], [-0.341, 0.145], [-1.016, 0.412], [0, 0], [-18.334, -15.191], [0, 0], [0.567, -2.528], [7.33, -23.926], [0.273, -0.13], [0.394, -0.182], [0.336, -0.152], [0.286, -0.127], [0.476, -0.195], [0.596, -0.217]], "v": [[-45.556, -78.732], [-77.965, -26.326], [-58.06, 47.87], [-58.055, 47.878], [-58.347, 48.096], [-59.26, 48.76], [-60.48, 49.62], [-61.075, 50.023], [-62.372, 50.884], [-62.547, 50.992], [-72.02, 56.011], [-73.048, 56.439], [-76.119, 57.589], [-76.124, 57.582], [-96.368, -16.902], [-96.373, -16.909], [-94.399, -24.34], [-52.693, -76.304], [-51.872, -76.705], [-50.683, -77.244], [-49.666, -77.687], [-48.807, -78.048], [-47.372, -78.606]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 59, "s": [{"i": [[-0.617, -0.073], [1.873, -23.893], [-20.713, -13.987], [0, 0], [0.091, -0.085], [0.281, -0.261], [0.378, -0.339], [0.188, -0.163], [0.408, -0.348], [0.063, -0.044], [3.121, -1.817], [0.333, -0.186], [1.006, -0.488], [0, 0], [-2.576, 32.879], [0, 0], [-0.411, 2.636], [-17.676, 11.682], [-0.262, 0.18], [-0.383, 0.229], [-0.328, 0.191], [-0.28, 0.159], [-0.463, 0.239]], "o": [[-13.985, 13.934], [-2.561, 32.676], [0, 0], [-0.091, 0.085], [-0.283, 0.273], [-0.374, 0.358], [-0.174, 0.165], [-0.396, 0.362], [-0.05, 0.045], [-2.844, 2.406], [-0.328, 0.191], [-0.98, 0.549], [0, 0], [-20.942, -13.886], [0, 0], [0.216, -2.723], [4.05, -25.987], [0.26, -0.167], [0.377, -0.237], [0.322, -0.199], [0.274, -0.166], [0.459, -0.259], [0.578, -0.296]], "v": [[-71.515, -70.597], [-97.116, -11.815], [-66.066, 63.928], [-66.06, 63.935], [-66.327, 64.197], [-67.166, 64.999], [-68.289, 66.042], [-68.84, 66.533], [-70.043, 67.586], [-70.206, 67.72], [-79.173, 74.073], [-80.162, 74.64], [-83.138, 76.199], [-83.144, 76.192], [-114.583, 0.186], [-114.589, 0.178], [-113.64, -7.858], [-78.466, -67.233], [-77.683, -67.748], [-76.546, -68.451], [-75.569, -69.031], [-74.742, -69.509], [-73.355, -70.259]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 77, "s": [{"i": [[-0.578, -0.16], [5.346, -25.423], [-17.683, -17.769], [0, 0], [0.099, -0.079], [0.307, -0.243], [0.411, -0.314], [0.204, -0.15], [0.441, -0.32], [0.067, -0.038], [3.249, -1.539], [0.345, -0.156], [1.033, -0.391], [0, 0], [-7.357, 34.984], [0, 0], [-0.785, 2.777], [-18.609, 10.212], [-0.277, 0.159], [-0.4, 0.196], [-0.341, 0.162], [-0.291, 0.133], [-0.478, 0.196]], "o": [[-15.422, 13.12], [-7.312, 34.769], [0, 0], [-0.099, 0.079], [-0.311, 0.256], [-0.41, 0.336], [-0.191, 0.154], [-0.432, 0.337], [-0.054, 0.042], [-3.072, 2.208], [-0.341, 0.162], [-1.017, 0.46], [0, 0], [-17.916, -17.691], [0, 0], [0.612, -2.897], [7.737, -27.385], [0.273, -0.145], [0.395, -0.204], [0.337, -0.171], [0.286, -0.142], [0.477, -0.217], [0.596, -0.241]], "v": [[-44.48, -98.265], [-77.668, -38.497], [-59.321, 46.99], [-59.316, 46.998], [-59.61, 47.245], [-60.53, 47.996], [-61.757, 48.968], [-62.356, 49.422], [-63.661, 50.394], [-63.836, 50.517], [-73.34, 56.156], [-74.368, 56.634], [-77.44, 57.915], [-77.445, 57.907], [-96.124, -27.914], [-96.129, -27.923], [-94.026, -36.432], [-51.614, -95.57], [-50.791, -96.02], [-49.601, -96.624], [-48.582, -97.119], [-47.722, -97.523], [-46.286, -98.145]], "c": true}]}, {"t": 102, "s": [{"i": [[-0.44, -0.44], [17.71, -20.02], [-6.94, -24.99], [0, 0], [0.13, -0.02], [0.4, -0.06], [0.53, -0.07], [0.26, -0.03], [0.56, -0.06], [0.08, 0], [3.71, 0.29], [0.39, 0.04], [1.13, 0.18], [0, 0], [-24.37, 27.55], [0, 0], [-2.12, 2.08], [-21.96, -0.41], [-0.33, 0], [-0.46, -0.03], [-0.39, -0.03], [-0.33, -0.03], [-0.53, -0.07]], "o": [[-20.56, 3.83], [-24.22, 27.38], [0, 0], [-0.13, 0.02], [-0.41, 0.07], [-0.54, 0.09], [-0.25, 0.04], [-0.56, 0.08], [-0.07, 0.01], [-3.89, 0.4], [-0.39, -0.03], [-1.15, -0.11], [0, 0], [-7.19, -25.04], [0, 0], [2.02, -2.28], [20.86, -20.55], [0.32, 0.01], [0.46, 0.02], [0.39, 0.02], [0.33, 0.02], [0.54, 0.05], [0.66, 0.09]], "v": [[55.622, -61.19], [-4.608, -24.71], [-31.398, 61.26], [-31.398, 61.27], [-31.788, 61.34], [-32.998, 61.54], [-34.598, 61.78], [-35.368, 61.88], [-37.038, 62.08], [-37.258, 62.1], [-48.688, 62.27], [-49.858, 62.17], [-53.278, 61.74], [-53.278, 61.73], [-26.618, -24.71], [-26.618, -24.72], [-20.408, -31.26], [47.822, -62.44], [48.792, -62.42], [50.172, -62.35], [51.342, -62.27], [52.322, -62.19], [53.932, -62.01]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.173, 0.647, 0.553, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-1827-growing-plant').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [330.969, 166.09], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 50, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 1, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[23.72, -26.82], [20.69, -3.73], [0, 0], [0.13, -0.02], [0.4, -0.06], [0.53, -0.07], [0.26, -0.03], [0.56, -0.06], [0.08, 0], [3.71, 0.28], [0.39, 0.04], [1.13, 0.18], [-24.37, 27.55], [0, 0], [-2.13, 2.08], [-21.96, -0.42], [-0.33, 0], [-0.46, -0.03], [-0.39, -0.03], [-0.33, -0.03], [-0.53, -0.07], [-0.44, -0.44], [-0.14, -0.57]], "o": [[-17.84, 20.16], [0, 0], [-0.13, 0.02], [-0.41, 0.07], [-0.54, 0.09], [-0.25, 0.04], [-0.56, 0.08], [-0.07, 0.01], [-3.9, 0.39], [-0.39, -0.03], [-1.16, -0.12], [-7.19, -25.04], [0, 0], [2.01, -2.28], [20.85, -20.55], [0.32, 0.01], [0.46, 0.02], [0.39, 0.02], [0.33, 0.02], [0.54, 0.05], [0.66, 0.09], [0.41, 0.39], [6.14, 24.82]], "v": [[27.948, 24.704], [-32.722, 61.264], [-32.722, 61.274], [-33.112, 61.344], [-34.322, 61.544], [-35.922, 61.784], [-36.692, 61.884], [-38.362, 62.084], [-38.582, 62.104], [-50.012, 62.274], [-51.182, 62.174], [-54.602, 61.734], [-27.942, -24.706], [-27.942, -24.716], [-21.732, -31.256], [46.498, -62.436], [47.468, -62.416], [48.848, -62.346], [50.018, -62.266], [50.998, -62.186], [52.608, -62.006], [54.298, -61.186], [55.138, -59.726]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 17, "s": [{"i": [[2.856, -35.69], [14.298, -15.413], [0, 0], [0.092, -0.094], [0.284, -0.288], [0.382, -0.374], [0.19, -0.18], [0.412, -0.384], [0.064, -0.048], [3.134, -2.005], [0.336, -0.202], [1.011, -0.535], [-2.927, 36.665], [0, 0], [-0.453, 2.942], [-17.806, 12.859], [-0.264, 0.198], [-0.386, 0.252], [-0.33, 0.21], [-0.282, 0.174], [-0.466, 0.263], [-0.616, -0.087], [-0.455, -0.371]], "o": [[-2.147, 26.834], [0, 0], [-0.092, 0.094], [-0.286, 0.302], [-0.378, 0.396], [-0.176, 0.182], [-0.4, 0.401], [-0.05, 0.05], [-2.883, 2.655], [-0.33, 0.21], [-0.999, 0.601], [-20.793, -15.696], [0, 0], [0.237, -3.03], [4.319, -28.955], [0.262, -0.184], [0.38, -0.26], [0.324, -0.218], [0.276, -0.182], [0.462, -0.284], [0.582, -0.325], [0.562, 0.065], [19.821, 16.151]], "v": [[-40.763, -22.627], [-67.293, 43.051], [-67.287, 43.059], [-67.556, 43.35], [-68.403, 44.237], [-69.538, 45.39], [-70.094, 45.932], [-71.308, 47.096], [-71.472, 47.244], [-80.507, 54.247], [-81.502, 54.871], [-84.5, 56.574], [-115.127, -28.542], [-115.133, -28.55], [-114.099, -37.509], [-78.293, -103.429], [-77.506, -103.996], [-76.36, -104.769], [-75.377, -105.408], [-74.546, -105.933], [-73.151, -106.756], [-71.307, -107.116], [-69.758, -106.454]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 40, "s": [{"i": [[6.645, -29.723], [15.465, -11.581], [0, 0], [0.099, -0.07], [0.305, -0.215], [0.409, -0.278], [0.203, -0.133], [0.439, -0.284], [0.067, -0.034], [3.24, -1.384], [0.346, -0.138], [1.033, -0.352], [-6.82, 30.536], [0, 0], [-0.757, 2.43], [-18.568, 9.096], [-0.276, 0.141], [-0.399, 0.175], [-0.341, 0.145], [-0.291, 0.119], [-0.478, 0.176], [-0.584, -0.133], [-0.398, -0.355]], "o": [[-4.996, 22.348], [0, 0], [-0.099, 0.07], [-0.308, 0.227], [-0.407, 0.297], [-0.189, 0.136], [-0.429, 0.298], [-0.054, 0.037], [-3.069, 1.955], [-0.341, 0.145], [-1.029, 0.409], [-18.334, -15.191], [0, 0], [0.559, -2.524], [7.321, -23.921], [0.273, -0.13], [0.394, -0.182], [0.336, -0.152], [0.286, -0.127], [0.476, -0.195], [0.596, -0.217], [0.535, 0.109], [17.348, 15.48]], "v": [[-26.646, -4.778], [-59.384, 47.874], [-59.379, 47.882], [-59.671, 48.1], [-60.584, 48.764], [-61.804, 49.624], [-62.398, 50.027], [-63.696, 50.888], [-63.87, 50.996], [-73.344, 56.014], [-74.371, 56.443], [-77.447, 57.586], [-97.692, -16.898], [-97.697, -16.905], [-95.723, -24.336], [-54.016, -76.3], [-53.195, -76.701], [-52.007, -77.241], [-50.989, -77.683], [-50.131, -78.044], [-48.696, -78.603], [-46.879, -78.728], [-45.458, -78.022]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 59, "s": [{"i": [[2.515, -32.005], [14.15, -13.934], [0, 0], [0.091, -0.085], [0.281, -0.261], [0.378, -0.339], [0.188, -0.163], [0.408, -0.348], [0.063, -0.044], [3.115, -1.824], [0.334, -0.184], [1.006, -0.488], [-2.576, 32.879], [0, 0], [-0.425, 2.64], [-17.683, 11.675], [-0.262, 0.18], [-0.383, 0.229], [-0.328, 0.191], [-0.28, 0.159], [-0.463, 0.239], [-0.617, -0.073], [-0.459, -0.328]], "o": [[-1.891, 24.064], [0, 0], [-0.091, 0.085], [-0.283, 0.273], [-0.374, 0.358], [-0.174, 0.165], [-0.396, 0.362], [-0.05, 0.045], [-2.858, 2.404], [-0.328, 0.191], [-0.994, 0.547], [-20.942, -13.886], [0, 0], [0.208, -2.717], [4.042, -25.982], [0.26, -0.167], [0.377, -0.237], [0.322, -0.199], [0.274, -0.166], [0.459, -0.259], [0.578, -0.296], [0.563, 0.054], [19.975, 14.302]], "v": [[-41.489, 4.853], [-67.39, 63.932], [-67.384, 63.939], [-67.651, 64.201], [-68.489, 65.003], [-69.613, 66.046], [-70.163, 66.537], [-71.367, 67.59], [-71.529, 67.724], [-80.497, 74.077], [-81.486, 74.644], [-84.468, 76.196], [-115.906, 0.189], [-115.912, 0.182], [-114.964, -7.854], [-79.789, -67.229], [-79.007, -67.744], [-77.87, -68.447], [-76.892, -69.028], [-76.066, -69.505], [-74.679, -70.255], [-72.839, -70.593], [-71.284, -70.013]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 77, "s": [{"i": [[7.168, -34.053], [15.579, -13.098], [0, 0], [0.099, -0.079], [0.307, -0.243], [0.411, -0.314], [0.204, -0.15], [0.441, -0.32], [0.067, -0.038], [3.244, -1.547], [0.346, -0.153], [1.033, -0.391], [-7.357, 34.984], [0, 0], [-0.799, 2.781], [-18.614, 10.204], [-0.277, 0.159], [-0.4, 0.196], [-0.341, 0.162], [-0.291, 0.133], [-0.478, 0.196], [-0.578, -0.16], [-0.389, -0.413]], "o": [[-5.389, 25.604], [0, 0], [-0.099, 0.079], [-0.311, 0.256], [-0.41, 0.336], [-0.191, 0.154], [-0.432, 0.337], [-0.054, 0.042], [-3.085, 2.205], [-0.341, 0.162], [-1.03, 0.456], [-17.916, -17.691], [0, 0], [0.603, -2.892], [7.728, -27.38], [0.273, -0.145], [0.395, -0.204], [0.337, -0.171], [0.286, -0.142], [0.477, -0.217], [0.596, -0.241], [0.529, 0.132], [16.931, 18.01]], "v": [[-27.126, -13.053], [-60.645, 46.994], [-60.64, 47.002], [-60.934, 47.249], [-61.853, 48], [-63.081, 48.971], [-63.679, 49.426], [-64.985, 50.398], [-65.16, 50.521], [-74.663, 56.16], [-75.692, 56.638], [-78.769, 57.911], [-97.448, -27.91], [-97.453, -27.919], [-95.35, -36.428], [-52.938, -95.566], [-52.115, -96.016], [-50.924, -96.62], [-49.905, -97.115], [-49.046, -97.519], [-47.61, -98.141], [-45.804, -98.261], [-44.406, -97.432]], "c": true}]}, {"t": 102, "s": [{"i": [[23.72, -26.82], [20.69, -3.73], [0, 0], [0.13, -0.02], [0.4, -0.06], [0.53, -0.07], [0.26, -0.03], [0.56, -0.06], [0.08, 0], [3.71, 0.28], [0.39, 0.04], [1.13, 0.18], [-24.37, 27.55], [0, 0], [-2.13, 2.08], [-21.96, -0.42], [-0.33, 0], [-0.46, -0.03], [-0.39, -0.03], [-0.33, -0.03], [-0.53, -0.07], [-0.44, -0.44], [-0.14, -0.57]], "o": [[-17.84, 20.16], [0, 0], [-0.13, 0.02], [-0.41, 0.07], [-0.54, 0.09], [-0.25, 0.04], [-0.56, 0.08], [-0.07, 0.01], [-3.9, 0.39], [-0.39, -0.03], [-1.16, -0.12], [-7.19, -25.04], [0, 0], [2.01, -2.28], [20.85, -20.55], [0.32, 0.01], [0.46, 0.02], [0.39, 0.02], [0.33, 0.02], [0.54, 0.05], [0.66, 0.09], [0.41, 0.39], [6.14, 24.82]], "v": [[27.948, 24.704], [-32.722, 61.264], [-32.722, 61.274], [-33.112, 61.344], [-34.322, 61.544], [-35.922, 61.784], [-36.692, 61.884], [-38.362, 62.084], [-38.582, 62.104], [-50.012, 62.274], [-51.182, 62.174], [-54.602, 61.734], [-27.942, -24.706], [-27.942, -24.716], [-21.732, -31.256], [46.498, -62.436], [47.468, -62.416], [48.848, -62.346], [50.018, -62.266], [50.998, -62.186], [52.608, -62.006], [54.298, -61.186], [55.138, -59.726]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.173, 0.647, 0.553, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-1827-growing-plant').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [332.293, 166.086], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 103, "st": 0, "bm": 0}]}], "layers": [{"ddd": 0, "ind": 1, "ty": 3, "nm": "control", "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "stroke", "np": 3, "mn": "Pseudo/@@VR1SVRgFQneM+9w2dmMkCw", "ix": 1, "en": 1, "ef": [{"ty": 7, "nm": "<PERSON><PERSON>", "mn": "Pseudo/@@VR1SVRgFQneM+9w2dmMkCw-0001", "ix": 1, "v": {"a": 0, "k": 1, "ix": 1}}]}, {"ty": 5, "nm": "primary", "np": 3, "mn": "ADBE Color Control", "ix": 2, "en": 1, "ef": [{"ty": 2, "nm": "Color", "mn": "ADBE Color Control-0001", "ix": 1, "v": {"a": 0, "k": [0.071, 0.075, 0.192], "ix": 1}}]}, {"ty": 5, "nm": "secondary", "np": 3, "mn": "ADBE Color Control", "ix": 3, "en": 1, "ef": [{"ty": 2, "nm": "Color", "mn": "ADBE Color Control-0001", "ix": 1, "v": {"a": 0, "k": [0.173, 0.647, 0.553], "ix": 1}}]}, {"ty": 5, "nm": "tertiary", "np": 3, "mn": "ADBE Color Control", "ix": 4, "en": 1, "ef": [{"ty": 2, "nm": "Color", "mn": "ADBE Color Control-0001", "ix": 1, "v": {"a": 0, "k": [0.698, 0.408, 0.212], "ix": 1}}]}, {"ty": 5, "nm": "quaternary", "np": 3, "mn": "ADBE Color Control", "ix": 5, "en": 1, "ef": [{"ty": 2, "nm": "Color", "mn": "ADBE Color Control-0001", "ix": 1, "v": {"a": 0, "k": [0.573, 0.078, 0.047], "ix": 1}}]}], "ip": 0, "op": 151, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 0, "nm": "hover-pinch", "refId": "comp_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [215, 215, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [215, 215, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 430, "h": 430, "ip": 0, "op": 160, "st": 0, "bm": 0}], "markers": [{"tm": 0, "cm": "default:hover-pinch", "dr": 150}]}