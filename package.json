{"name": "breathair", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "start": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "devDependencies": {"@vitejs/plugin-react": "^3.1.0", "autoprefixer": "^10.4.19", "eslint": "^8.42.0", "eslint-plugin-react": "^7.32.0", "postcss": "^8.4.40", "tailwindcss": "^3.4.7", "vite": "^4.4.0"}, "dependencies": {"@reduxjs/toolkit": "^2.2.7", "axios": "^1.7.2", "axois": "^0.0.1-security", "breathair": "file:", "dompurify": "^3.1.6", "dotenv": "^16.4.5", "framer-motion": "^11.7.0", "jodit": "^4.2.27", "jodit-react": "^4.1.2", "lottie-react": "^2.4.0", "lucide-react": "^0.525.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hot-toast": "^2.4.1", "react-icons": "^5.2.1", "react-redux": "^9.1.2", "react-router-dom": "^6.25.1", "redux-toolkit": "^1.1.2"}}