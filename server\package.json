{"name": "server", "version": "1.0.0", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "build": "echo 'No build step needed for Node.js backend'"}, "author": "", "license": "ISC", "description": "", "dependencies": {"bcrypt": "^5.1.1", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.19.2", "jsonwebtoken": "^9.0.2", "mongoose": "^8.5.2", "nodemailer": "^6.9.14", "nodemon": "^3.1.4", "razorpay": "^2.9.4", "server": "file:"}}